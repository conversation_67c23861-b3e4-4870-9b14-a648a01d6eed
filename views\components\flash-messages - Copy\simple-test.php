<?php
/**
 * 🧪 SIMPLE TEST - ULTIMATE FLASH MESSAGES
 * Test the single file flash messages system
 */

// Include the ultimate flash system (ONLY ONE FILE NEEDED!)
require_once __DIR__ . '/ultimate-flash.php';

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'success':
            flash_success('Data saved successfully!', 'Success');
            break;
        case 'error':
            flash_error('Something went wrong!', 'Error');
            break;
        case 'warning':
            flash_warning('Please check your input', 'Warning');
            break;
        case 'info':
            flash_info('Here is some information', 'Information');
            break;
        case 'achievement':
            flash_achievement('You unlocked a new badge!', 'Achievement Unlocked');
            break;
        case 'login':
            UltimateFlash::loginSuccess('john_doe');
            break;
        case 'payment':
            UltimateFlash::paymentSuccess('$99.99');
            break;
        case 'upload':
            UltimateFlash::fileUploaded('document.pdf');
            break;
        case 'quiz':
            UltimateFlash::quizCompleted('85%');
            break;
        case 'multiple':
            flash_success('First message');
            flash_info('Second message');
            flash_warning('Third message');
            break;
        case 'clear':
            UltimateFlash::clear();
            break;
    }
    
    // Redirect to prevent form resubmission
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

// Set theme (optional)
UltimateFlash::$theme = 'modern'; // modern, glass, neon, minimal
UltimateFlash::$position = 'top-right'; // top-right, top-left, bottom-right, bottom-left, top-center, bottom-center

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultimate Flash Messages - Simple Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            color: white;
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .subtitle {
            text-align: center;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .test-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .test-btn.success { background: linear-gradient(45deg, #10b981, #059669); }
        .test-btn.error { background: linear-gradient(45deg, #ef4444, #dc2626); }
        .test-btn.warning { background: linear-gradient(45deg, #f59e0b, #d97706); }
        .test-btn.info { background: linear-gradient(45deg, #3b82f6, #2563eb); }
        .test-btn.special { background: linear-gradient(45deg, #8b5cf6, #7c3aed); }
        
        .stats {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }
        
        .theme-selector {
            margin: 20px 0;
            text-align: center;
        }
        
        .theme-selector select {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 12px;
            border-radius: 6px;
            margin: 0 10px;
        }
        
        .theme-selector option {
            background: #333;
            color: white;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Ultimate Flash Messages</h1>
        <p class="subtitle">Single File • Zero Setup • 100+ Functions • Beautiful Themes</p>
        
        <!-- Theme Selector -->
        <div class="theme-selector">
            <label>Theme:</label>
            <select onchange="changeTheme(this.value)">
                <option value="modern">Modern</option>
                <option value="glass">Glass</option>
                <option value="neon">Neon</option>
                <option value="minimal">Minimal</option>
            </select>
            
            <label>Position:</label>
            <select onchange="changePosition(this.value)">
                <option value="top-right">Top Right</option>
                <option value="top-left">Top Left</option>
                <option value="top-center">Top Center</option>
                <option value="bottom-right">Bottom Right</option>
                <option value="bottom-left">Bottom Left</option>
                <option value="bottom-center">Bottom Center</option>
            </select>
        </div>
        
        <!-- Test Buttons -->
        <div class="test-grid">
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="success">
                <button type="submit" class="test-btn success">✅ Success Message</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="error">
                <button type="submit" class="test-btn error">❌ Error Message</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="warning">
                <button type="submit" class="test-btn warning">⚠️ Warning Message</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="info">
                <button type="submit" class="test-btn info">ℹ️ Info Message</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="achievement">
                <button type="submit" class="test-btn special">🏆 Achievement</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="login">
                <button type="submit" class="test-btn success">🔑 Login Success</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="payment">
                <button type="submit" class="test-btn success">💰 Payment Success</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="upload">
                <button type="submit" class="test-btn info">📁 File Uploaded</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="quiz">
                <button type="submit" class="test-btn special">📚 Quiz Complete</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="multiple">
                <button type="submit" class="test-btn special">📦 Multiple Messages</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="clear">
                <button type="submit" class="test-btn error">🗑️ Clear All</button>
            </form>
        </div>
        
        <!-- Statistics -->
        <div class="stats">
            <h3>📊 System Status</h3>
            <p><strong>Active Messages:</strong> <?= UltimateFlash::count() ?></p>
            <p><strong>Theme:</strong> <?= UltimateFlash::$theme ?></p>
            <p><strong>Position:</strong> <?= UltimateFlash::$position ?></p>
            <p><strong>Setup Time:</strong> 0 seconds (Zero setup required!)</p>
            <p><strong>Files Required:</strong> 1 (ultimate-flash.php)</p>
        </div>
        
        <!-- Usage Instructions -->
        <div class="stats">
            <h3>🎯 How to Use</h3>
            <p><strong>1. Include:</strong> require_once 'ultimate-flash.php';</p>
            <p><strong>2. Add Messages:</strong> flash_success('Message');</p>
            <p><strong>3. Display:</strong> echo flash_render();</p>
            <p><strong>That's it!</strong> No configuration needed!</p>
        </div>
    </div>
    
    <!-- IMPORTANT: Render flash messages -->
    <?= flash_render() ?>
    
    <script>
        function changeTheme(theme) {
            // This would require a page reload to change PHP settings
            // For demo purposes, we'll just show an alert
            alert('Theme changed to: ' + theme + '\nReload the page to see the change.');
        }
        
        function changePosition(position) {
            // This would require a page reload to change PHP settings
            // For demo purposes, we'll just show an alert
            alert('Position changed to: ' + position + '\nReload the page to see the change.');
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        document.querySelector('input[value="success"]').closest('form').submit();
                        break;
                    case '2':
                        e.preventDefault();
                        document.querySelector('input[value="error"]').closest('form').submit();
                        break;
                    case '3':
                        e.preventDefault();
                        document.querySelector('input[value="warning"]').closest('form').submit();
                        break;
                    case '4':
                        e.preventDefault();
                        document.querySelector('input[value="info"]').closest('form').submit();
                        break;
                }
            }
        });
        
        console.log('🚀 Ultimate Flash Messages Test Page Loaded');
        console.log('⌨️ Keyboard Shortcuts: Ctrl+1,2,3,4 for different message types');
        console.log('📁 Only one file needed: ultimate-flash.php');
    </script>
</body>
</html>
