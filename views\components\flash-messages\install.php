<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES - ONE-CLICK INSTALLER
 * Easy Installation System for Any Website
 */

session_start();

// Installation steps
$steps = [
    1 => 'System Check',
    2 => 'File Setup', 
    3 => 'Configuration',
    4 => 'Complete'
];

$currentStep = $_GET['step'] ?? 1;
$errors = [];
$warnings = [];

// Handle installation actions
if ($_POST['action'] ?? '' === 'install') {
    $currentStep = performInstallation();
}

function performInstallation() {
    global $errors, $warnings;
    
    // Step 1: System Check
    if (!checkSystemRequirements()) {
        return 1;
    }
    
    // Step 2: File Setup
    if (!setupFiles()) {
        return 2;
    }
    
    // Step 3: Configuration
    if (!setupConfiguration()) {
        return 3;
    }
    
    return 4; // Complete
}

function checkSystemRequirements() {
    global $errors, $warnings;
    
    // Check PHP version
    if (version_compare(PHP_VERSION, '7.4.0', '<')) {
        $errors[] = 'PHP 7.4+ required. Current: ' . PHP_VERSION;
    }
    
    // Check write permissions
    if (!is_writable(__DIR__)) {
        $errors[] = 'Directory not writable: ' . __DIR__;
    }
    
    // Check required extensions
    $required = ['json', 'session'];
    foreach ($required as $ext) {
        if (!extension_loaded($ext)) {
            $errors[] = "Required PHP extension missing: {$ext}";
        }
    }
    
    // Warnings for optional features
    if (!extension_loaded('mbstring')) {
        $warnings[] = 'mbstring extension recommended for better text handling';
    }
    
    return empty($errors);
}

function setupFiles() {
    global $errors;
    
    $files = [
        'ultimate-flash.php' => 'Main flash message system',
        'setup.php' => 'Settings manager and demo',
        'QUICK-GUIDE.md' => 'Documentation'
    ];
    
    foreach ($files as $file => $desc) {
        if (!file_exists(__DIR__ . '/' . $file)) {
            $errors[] = "Missing file: {$file} ({$desc})";
        }
    }
    
    // Create backup directory
    $backupDir = __DIR__ . '/backups';
    if (!is_dir($backupDir)) {
        if (!mkdir($backupDir, 0755, true)) {
            $errors[] = 'Could not create backup directory';
        }
    }
    
    // Create logs directory
    $logsDir = __DIR__ . '/logs';
    if (!is_dir($logsDir)) {
        if (!mkdir($logsDir, 0755, true)) {
            $errors[] = 'Could not create logs directory';
        }
    }
    
    return empty($errors);
}

function setupConfiguration() {
    global $errors;
    
    // Create default config file
    $config = [
        'installed' => true,
        'version' => '4.0',
        'install_date' => date('Y-m-d H:i:s'),
        'analytics_enabled' => true,
        'smart_positioning' => true,
        'mobile_gestures' => true,
        'themes' => ['modern', 'glass', 'neon', 'minimal', 'dark', 'light', 'corporate', 'gaming', 'ecommerce', 'educational', 'medical']
    ];
    
    $configFile = __DIR__ . '/config.json';
    if (!file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT))) {
        $errors[] = 'Could not create configuration file';
    }
    
    return empty($errors);
}

function getSystemInfo() {
    return [
        'PHP Version' => PHP_VERSION,
        'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'Document Root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'Current Directory' => __DIR__,
        'Memory Limit' => ini_get('memory_limit'),
        'Max Execution Time' => ini_get('max_execution_time') . 's',
        'File Uploads' => ini_get('file_uploads') ? 'Enabled' : 'Disabled',
        'Session Support' => extension_loaded('session') ? 'Yes' : 'No'
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultimate Flash Messages - Installer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .installer {
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            max-width: 800px;
            width: 100%;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .progress {
            display: flex;
            background: #f8fafc;
            padding: 0;
        }
        
        .step {
            flex: 1;
            padding: 20px;
            text-align: center;
            position: relative;
            border-right: 1px solid #e2e8f0;
        }
        
        .step:last-child {
            border-right: none;
        }
        
        .step.active {
            background: #3b82f6;
            color: white;
        }
        
        .step.completed {
            background: #10b981;
            color: white;
        }
        
        .content {
            padding: 40px;
        }
        
        .system-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: #f8fafc;
            border-radius: 8px;
        }
        
        .error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #991b1b;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .warning {
            background: #fffbeb;
            border: 1px solid #fed7aa;
            color: #92400e;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .success {
            background: #ecfdf5;
            border: 1px solid #a7f3d0;
            color: #065f46;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .btn {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .actions {
            text-align: center;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="installer">
        <div class="header">
            <h1>🚀 Ultimate Flash Messages</h1>
            <p>One-Click Installation System</p>
        </div>
        
        <div class="progress">
            <?php foreach ($steps as $num => $name): ?>
                <div class="step <?= $num < $currentStep ? 'completed' : ($num == $currentStep ? 'active' : '') ?>">
                    <strong><?= $num ?></strong><br>
                    <?= $name ?>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="content">
            <?php if ($currentStep == 1): ?>
                <h2>🔍 System Requirements Check</h2>
                
                <?php if (!empty($errors)): ?>
                    <?php foreach ($errors as $error): ?>
                        <div class="error">❌ <?= htmlspecialchars($error) ?></div>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <?php if (!empty($warnings)): ?>
                    <?php foreach ($warnings as $warning): ?>
                        <div class="warning">⚠️ <?= htmlspecialchars($warning) ?></div>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <?php if (empty($errors)): ?>
                    <div class="success">✅ All system requirements met!</div>
                <?php endif; ?>
                
                <h3>System Information:</h3>
                <div class="system-info">
                    <?php foreach (getSystemInfo() as $key => $value): ?>
                        <div class="info-item">
                            <strong><?= $key ?>:</strong>
                            <span><?= htmlspecialchars($value) ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
                
            <?php elseif ($currentStep == 2): ?>
                <h2>📁 File Setup</h2>
                <p>Setting up required files and directories...</p>
                
            <?php elseif ($currentStep == 3): ?>
                <h2>⚙️ Configuration</h2>
                <p>Creating configuration files...</p>
                
            <?php elseif ($currentStep == 4): ?>
                <h2>🎉 Installation Complete!</h2>
                <div class="success">
                    ✅ Ultimate Flash Messages has been successfully installed!
                </div>
                
                <h3>Next Steps:</h3>
                <ul style="margin: 20px 0; padding-left: 20px;">
                    <li>Visit <a href="setup.php">setup.php</a> to customize settings</li>
                    <li>Read <a href="QUICK-GUIDE.md">QUICK-GUIDE.md</a> for usage instructions</li>
                    <li>Include in your project: <code>require_once 'ultimate-flash.php';</code></li>
                </ul>
                
                <h3>Quick Test:</h3>
                <pre style="background: #f8fafc; padding: 15px; border-radius: 8px; margin: 15px 0;">
&lt;?php
require_once 'ultimate-flash.php';
UltimateFlash::success('Installation successful!');
echo UltimateFlash::render();
?&gt;</pre>
            <?php endif; ?>
            
            <div class="actions">
                <?php if ($currentStep < 4): ?>
                    <form method="post">
                        <input type="hidden" name="action" value="install">
                        <button type="submit" class="btn" <?= !empty($errors) ? 'disabled' : '' ?>>
                            <?= $currentStep == 1 ? 'Start Installation' : 'Continue' ?>
                        </button>
                    </form>
                <?php else: ?>
                    <a href="setup.php" class="btn" style="text-decoration: none; display: inline-block;">
                        🚀 Open Settings Manager
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
