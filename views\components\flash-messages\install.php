<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES - ONE-CL<PERSON>K INSTALLER
 * Easy Installation System for Any Website
 */

session_start();

// Installation steps
$steps = [
    1 => 'System Check',
    2 => 'File Setup', 
    3 => 'Configuration',
    4 => 'Complete'
];

$currentStep = $_GET['step'] ?? 1;
$errors = [];
$warnings = [];

// Handle installation actions
if ($_POST['action'] ?? '' === 'install') {
    $currentStep = performInstallation();
}

function performInstallation() {
    global $errors, $warnings;
    
    // Step 1: System Check
    if (!checkSystemRequirements()) {
        return 1;
    }
    
    // Step 2: File Setup
    if (!setupFiles()) {
        return 2;
    }
    
    // Step 3: Configuration
    if (!setupConfiguration()) {
        return 3;
    }
    
    return 4; // Complete
}

function checkSystemRequirements() {
    global $errors, $warnings;

    // Check PHP version
    if (version_compare(PHP_VERSION, '7.4.0', '<')) {
        $errors[] = "❌ <strong>PHP Version Issue</strong><br>
                   📋 Required: PHP 7.4 or higher<br>
                   📋 Current: PHP " . PHP_VERSION . "<br>
                   💡 Solution: Upgrade PHP to version 7.4+ through your hosting control panel or contact your host";
    }

    // Check required extensions
    $required = [
        'json' => 'JSON encoding/decoding for settings',
        'session' => 'Session management for flash messages'
    ];

    foreach ($required as $ext => $purpose) {
        if (!extension_loaded($ext)) {
            $errors[] = "❌ <strong>Missing PHP Extension: {$ext}</strong><br>
                       📋 Purpose: {$purpose}<br>
                       💡 Solution: Enable {$ext} extension in php.ini or contact your hosting provider";
        }
    }

    // Check optional extensions
    $optional = [
        'mbstring' => 'Better text handling for international characters',
        'curl' => 'HTTP requests for analytics (if using external endpoints)'
    ];

    foreach ($optional as $ext => $purpose) {
        if (!extension_loaded($ext)) {
            $warnings[] = "⚠️ <strong>Optional Extension Missing: {$ext}</strong><br>
                         📋 Purpose: {$purpose}<br>
                         💡 Recommendation: Enable for enhanced functionality";
        }
    }

    // Check server software
    $server = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
    if (stripos($server, 'apache') === false && stripos($server, 'nginx') === false) {
        $warnings[] = "⚠️ <strong>Server Software</strong><br>
                     📋 Detected: {$server}<br>
                     💡 Note: Tested on Apache and Nginx. Should work on most servers.";
    }

    return empty($errors);
}

function setupFiles() {
    global $errors;

    $files = [
        'ultimate-flash.php' => [
            'desc' => 'Main flash message system',
            'required' => true,
            'solution' => 'Download ultimate-flash.php from the package and place it in this directory'
        ],
        'setup.php' => [
            'desc' => 'Settings manager and demo',
            'required' => true,
            'solution' => 'Download setup.php from the package and place it in this directory'
        ],
        'QUICK-GUIDE.md' => [
            'desc' => 'Documentation',
            'required' => false,
            'solution' => 'Download QUICK-GUIDE.md for usage instructions (optional)'
        ]
    ];

    foreach ($files as $file => $info) {
        if (!file_exists(__DIR__ . '/' . $file)) {
            if ($info['required']) {
                $errors[] = "❌ Missing required file: <strong>{$file}</strong><br>
                           📝 Description: {$info['desc']}<br>
                           💡 Solution: {$info['solution']}";
            } else {
                $warnings[] = "⚠️ Optional file missing: <strong>{$file}</strong><br>
                             📝 Description: {$info['desc']}<br>
                             💡 Solution: {$info['solution']}";
            }
        }
    }

    // Check directory permissions
    if (!is_writable(__DIR__)) {
        $errors[] = "❌ Directory not writable: <strong>" . __DIR__ . "</strong><br>
                   💡 Solution: Set directory permissions to 755 or 777<br>
                   🔧 Command: <code>chmod 755 " . basename(__DIR__) . "</code>";
    }

    return empty($errors);
}

function setupConfiguration() {
    global $errors;

    // No configuration files needed - everything works out of the box
    // Configuration is handled by the main system

    return true; // Always successful
}

function getSystemInfo() {
    return [
        'PHP Version' => PHP_VERSION,
        'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'Document Root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'Current Directory' => __DIR__,
        'Memory Limit' => ini_get('memory_limit'),
        'Max Execution Time' => ini_get('max_execution_time') . 's',
        'File Uploads' => ini_get('file_uploads') ? 'Enabled' : 'Disabled',
        'Session Support' => extension_loaded('session') ? 'Yes' : 'No'
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultimate Flash Messages - Installer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .installer {
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            max-width: 800px;
            width: 100%;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .progress {
            display: flex;
            background: #f8fafc;
            padding: 0;
        }
        
        .step {
            flex: 1;
            padding: 20px;
            text-align: center;
            position: relative;
            border-right: 1px solid #e2e8f0;
        }
        
        .step:last-child {
            border-right: none;
        }
        
        .step.active {
            background: #3b82f6;
            color: white;
        }
        
        .step.completed {
            background: #10b981;
            color: white;
        }
        
        .content {
            padding: 40px;
        }
        
        .system-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: #f8fafc;
            border-radius: 8px;
        }
        
        .error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #991b1b;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .warning {
            background: #fffbeb;
            border: 1px solid #fed7aa;
            color: #92400e;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .success {
            background: #ecfdf5;
            border: 1px solid #a7f3d0;
            color: #065f46;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .btn {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .actions {
            text-align: center;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="installer">
        <div class="header">
            <h1>🚀 Ultimate Flash Messages</h1>
            <p>One-Click Installation System</p>
        </div>
        
        <div class="progress">
            <?php foreach ($steps as $num => $name): ?>
                <div class="step <?= $num < $currentStep ? 'completed' : ($num == $currentStep ? 'active' : '') ?>">
                    <strong><?= $num ?></strong><br>
                    <?= $name ?>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="content">
            <?php if ($currentStep == 1): ?>
                <h2>🔍 System Requirements Check</h2>
                
                <?php if (!empty($errors)): ?>
                    <div style="margin-bottom: 20px;">
                        <h3 style="color: #ef4444; margin-bottom: 15px;">❌ Issues Found:</h3>
                        <?php foreach ($errors as $error): ?>
                            <div class="error" style="margin-bottom: 15px; line-height: 1.6;">
                                <?= $error ?>
                            </div>
                        <?php endforeach; ?>

                        <div style="background: #fef2f2; border: 1px solid #fecaca; color: #991b1b; padding: 15px; border-radius: 8px; margin-top: 20px;">
                            <h4>🛠️ How to Fix:</h4>
                            <ol style="margin: 10px 0; padding-left: 20px;">
                                <li>Follow the solutions provided above for each issue</li>
                                <li>Contact your hosting provider if you need help with server configuration</li>
                                <li>Refresh this page after making changes</li>
                            </ol>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($warnings)): ?>
                    <div style="margin-bottom: 20px;">
                        <h3 style="color: #f59e0b; margin-bottom: 15px;">⚠️ Recommendations:</h3>
                        <?php foreach ($warnings as $warning): ?>
                            <div class="warning" style="margin-bottom: 15px; line-height: 1.6;">
                                <?= $warning ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <?php if (empty($errors)): ?>
                    <div class="success">✅ System ready for installation!</div>
                    <p>PHP <?= PHP_VERSION ?> • Directory writable • All requirements met</p>
                <?php endif; ?>
                
                <h3>System Information:</h3>
                <div class="system-info">
                    <?php foreach (getSystemInfo() as $key => $value): ?>
                        <div class="info-item">
                            <strong><?= $key ?>:</strong>
                            <span><?= htmlspecialchars($value) ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
                
            <?php elseif ($currentStep == 2): ?>
                <h2>📁 File Setup</h2>
                <p>Setting up required files and directories...</p>
                
            <?php elseif ($currentStep == 3): ?>
                <h2>⚙️ Configuration</h2>
                <p>Creating configuration files...</p>
                
            <?php elseif ($currentStep == 4): ?>
                <h2>🎉 Installation Complete!</h2>
                <div class="success">
                    ✅ Ultimate Flash Messages has been successfully installed!
                </div>
                
                <h3>Next Steps:</h3>
                <ul style="margin: 20px 0; padding-left: 20px;">
                    <li>Visit <a href="setup.php">setup.php</a> to customize settings</li>
                    <li>Read <a href="QUICK-GUIDE.md">QUICK-GUIDE.md</a> for usage instructions</li>
                    <li>Include in your project: <code>require_once 'ultimate-flash.php';</code></li>
                </ul>
                
                <h3>Quick Test:</h3>
                <pre style="background: #f8fafc; padding: 15px; border-radius: 8px; margin: 15px 0;">
&lt;?php
require_once 'ultimate-flash.php';
UltimateFlash::success('Installation successful!');
echo UltimateFlash::render();
?&gt;</pre>
            <?php endif; ?>
            
            <div class="actions">
                <?php if ($currentStep < 4): ?>
                    <form method="post">
                        <input type="hidden" name="action" value="install">
                        <button type="submit" class="btn" <?= !empty($errors) ? 'disabled' : '' ?>>
                            <?= $currentStep == 1 ? 'Start Installation' : 'Continue' ?>
                        </button>
                    </form>
                <?php else: ?>
                    <a href="setup.php" class="btn" style="text-decoration: none; display: inline-block;">
                        🚀 Open Settings Manager
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
