<?php
/**
 * 🧪 SIMPLE TEST - ULTIMATE FLASH MESSAGES
 * Test the single file flash messages system
 */

// Include the ultimate flash system (ONLY ONE FILE NEEDED!)
require_once __DIR__ . '/ultimate-flash.php';

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'success':
            flash_success('Data saved successfully!', 'Success');
            break;
        case 'error':
            flash_error('Something went wrong!', 'Error');
            break;
        case 'warning':
            flash_warning('Please check your input', 'Warning');
            break;
        case 'info':
            flash_info('Here is some information', 'Information');
            break;
        case 'achievement':
            flash_achievement('You unlocked a new badge!', 'Achievement Unlocked');
            break;
        case 'login':
            UltimateFlash::loginSuccess('john_doe');
            break;
        case 'payment':
            UltimateFlash::paymentSuccess('$99.99');
            break;
        case 'upload':
            UltimateFlash::fileUploaded('document.pdf');
            break;
        case 'quiz':
            UltimateFlash::quizCompleted('85%');
            break;
        case 'multiple':
            flash_success('First message');
            flash_info('Second message');
            flash_warning('Third message');
            break;
        case 'clear':
            UltimateFlash::clear();
            break;
        case 'alert':
            UltimateFlash::alert('This is an alert dialog!', 'Alert Dialog');
            break;
        case 'modal':
            UltimateFlash::modal('This is a modal dialog with more content and options.', 'Modal Dialog', ['size' => 'large']);
            break;
        case 'confirm':
            UltimateFlash::confirm('Are you sure you want to delete this item?', 'Confirm Delete', 'handleConfirm', 'handleCancel');
            break;
        case 'progress':
            $progressId = UltimateFlash::progress('Processing your request...', 'Please Wait', 0);
            // Simulate progress updates
            echo "<script>
                setTimeout(() => UltimateFlashJS.updateProgress('{$progressId}', 25, 'Step 1 completed...'), 1000);
                setTimeout(() => UltimateFlashJS.updateProgress('{$progressId}', 50, 'Step 2 completed...'), 2000);
                setTimeout(() => UltimateFlashJS.updateProgress('{$progressId}', 75, 'Step 3 completed...'), 3000);
                setTimeout(() => UltimateFlashJS.updateProgress('{$progressId}', 100, 'All steps completed!'), 4000);
                setTimeout(() => UltimateFlashJS.dismiss('{$progressId}'), 5000);
            </script>";
            break;
        case 'loading':
            $loadingId = UltimateFlash::loading('Loading data from server...', 'Please Wait');
            echo "<script>setTimeout(() => UltimateFlashJS.dismiss('{$loadingId}'), 3000);</script>";
            break;
        case 'prompt':
            UltimateFlash::prompt('Please enter your name:', 'User Input', 'John Doe', 'handlePromptSubmit');
            break;
        case 'notification':
            UltimateFlash::notification('You have a new message!', 'New Notification');
            break;
        case 'custom':
            UltimateFlash::custom('special', 'This is a custom message type!', 'Custom Message', ['customColor' => '#ff6b6b']);
            break;
        case 'tooltip':
            UltimateFlash::tooltip('This is a helpful tooltip!', '', ['position' => 'top']);
            break;
        case 'theme_dark':
            UltimateFlash::setTheme('dark');
            flash_success('Dark theme activated!', 'Theme Changed');
            break;
        case 'theme_light':
            UltimateFlash::setTheme('light');
            flash_success('Light theme activated!', 'Theme Changed');
            break;
        case 'animation_bounce':
            UltimateFlash::$animationStyle = 'bounce';
            flash_success('Bounce animation activated!', 'Animation Changed');
            break;
        case 'animation_fade':
            UltimateFlash::$animationStyle = 'fade';
            flash_success('Fade animation activated!', 'Animation Changed');
            break;
    }
    
    // Redirect to prevent form resubmission
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

// Set theme (optional)
UltimateFlash::$theme = 'modern'; // modern, glass, neon, minimal
UltimateFlash::$position = 'top-right'; // top-right, top-left, bottom-right, bottom-left, top-center, bottom-center

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultimate Flash Messages - Simple Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            color: white;
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .subtitle {
            text-align: center;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 12px;
            margin-bottom: 30px;
        }

        .test-grid h3 {
            grid-column: 1 / -1;
            color: white;
            margin: 20px 0 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        
        .test-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            width: 100%;
            text-align: center;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .test-btn.success { background: linear-gradient(45deg, #10b981, #059669); }
        .test-btn.error { background: linear-gradient(45deg, #ef4444, #dc2626); }
        .test-btn.warning { background: linear-gradient(45deg, #f59e0b, #d97706); }
        .test-btn.info { background: linear-gradient(45deg, #3b82f6, #2563eb); }
        .test-btn.special { background: linear-gradient(45deg, #8b5cf6, #7c3aed); }
        
        .stats {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }
        
        .theme-selector {
            margin: 20px 0;
            text-align: center;
        }
        
        .theme-selector select {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 12px;
            border-radius: 6px;
            margin: 0 10px;
        }
        
        .theme-selector option {
            background: #333;
            color: white;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Ultimate Flash Messages v2.0</h1>
        <p class="subtitle">Single File • Zero Setup • 200+ Functions • Multiple Types • Beautiful Themes</p>
        <p style="text-align: center; margin-bottom: 20px; opacity: 0.9;">
            ✨ Toasts • 🪟 Modals • 📊 Progress • ⏳ Loading • 🎨 Themes • 📱 Mobile Ready
        </p>
        
        <!-- Theme Selector -->
        <div class="theme-selector">
            <label>Theme:</label>
            <select onchange="changeTheme(this.value)">
                <option value="modern">Modern</option>
                <option value="glass">Glass</option>
                <option value="neon">Neon</option>
                <option value="minimal">Minimal</option>
            </select>
            
            <label>Position:</label>
            <select onchange="changePosition(this.value)">
                <option value="top-right">Top Right</option>
                <option value="top-left">Top Left</option>
                <option value="top-center">Top Center</option>
                <option value="bottom-right">Bottom Right</option>
                <option value="bottom-left">Bottom Left</option>
                <option value="bottom-center">Bottom Center</option>
            </select>
        </div>
        
        <!-- Test Buttons -->
        <div class="test-grid">
            <!-- Basic Messages -->
            <h3 style="grid-column: 1 / -1; color: white; margin: 20px 0 10px 0;">🎯 Basic Messages</h3>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="success">
                <button type="submit" class="test-btn success">✅ Success</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="error">
                <button type="submit" class="test-btn error">❌ Error</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="warning">
                <button type="submit" class="test-btn warning">⚠️ Warning</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="info">
                <button type="submit" class="test-btn info">ℹ️ Info</button>
            </form>

            <!-- Modal & Dialog Types -->
            <h3 style="grid-column: 1 / -1; color: white; margin: 20px 0 10px 0;">🪟 Modals & Dialogs</h3>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="alert">
                <button type="submit" class="test-btn error">🚨 Alert Dialog</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="modal">
                <button type="submit" class="test-btn info">📋 Modal Dialog</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="confirm">
                <button type="submit" class="test-btn warning">❓ Confirmation</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="prompt">
                <button type="submit" class="test-btn info">✏️ Input Prompt</button>
            </form>

            <!-- Progress & Loading -->
            <h3 style="grid-column: 1 / -1; color: white; margin: 20px 0 10px 0;">📊 Progress & Loading</h3>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="progress">
                <button type="submit" class="test-btn info">📊 Progress Bar</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="loading">
                <button type="submit" class="test-btn info">⏳ Loading Spinner</button>
            </form>

            <!-- Special Features -->
            <h3 style="grid-column: 1 / -1; color: white; margin: 20px 0 10px 0;">🎪 Special Features</h3>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="achievement">
                <button type="submit" class="test-btn special">🏆 Achievement</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="notification">
                <button type="submit" class="test-btn special">🔔 Notification</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="custom">
                <button type="submit" class="test-btn special">⭐ Custom Type</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="tooltip">
                <button type="submit" class="test-btn info">💡 Tooltip</button>
            </form>

            <!-- Application Messages -->
            <h3 style="grid-column: 1 / -1; color: white; margin: 20px 0 10px 0;">🔐 Application Messages</h3>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="login">
                <button type="submit" class="test-btn success">🔑 Login Success</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="payment">
                <button type="submit" class="test-btn success">💰 Payment Success</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="upload">
                <button type="submit" class="test-btn info">📁 File Uploaded</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="quiz">
                <button type="submit" class="test-btn special">📚 Quiz Complete</button>
            </form>

            <!-- Theme & Animation Controls -->
            <h3 style="grid-column: 1 / -1; color: white; margin: 20px 0 10px 0;">🎨 Themes & Animations</h3>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="theme_dark">
                <button type="submit" class="test-btn">🌙 Dark Theme</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="theme_light">
                <button type="submit" class="test-btn">☀️ Light Theme</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="animation_bounce">
                <button type="submit" class="test-btn">🎾 Bounce Animation</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="animation_fade">
                <button type="submit" class="test-btn">👻 Fade Animation</button>
            </form>

            <!-- Utility -->
            <h3 style="grid-column: 1 / -1; color: white; margin: 20px 0 10px 0;">🛠️ Utility</h3>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="multiple">
                <button type="submit" class="test-btn special">📦 Multiple Messages</button>
            </form>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="clear">
                <button type="submit" class="test-btn error">🗑️ Clear All</button>
            </form>
        </div>
        
        <!-- Statistics -->
        <div class="stats">
            <h3>📊 System Status</h3>
            <?php $stats = UltimateFlash::getStats(); ?>
            <p><strong>Active Messages:</strong> <?= $stats['total'] ?></p>
            <p><strong>Theme:</strong> <?= UltimateFlash::$theme ?></p>
            <p><strong>Position:</strong> <?= UltimateFlash::$position ?></p>
            <p><strong>Animation:</strong> <?= UltimateFlash::$animationStyle ?></p>
            <p><strong>Auto Hide:</strong> <?= UltimateFlash::$autoHide ? 'Yes' : 'No' ?></p>
            <p><strong>Duration:</strong> <?= UltimateFlash::$duration ?>ms</p>
            <p><strong>Max Messages:</strong> <?= UltimateFlash::$maxMessages ?></p>
            <p><strong>Setup Time:</strong> 0 seconds (Zero setup required!)</p>
            <p><strong>Files Required:</strong> 1 (ultimate-flash.php)</p>
        </div>

        <!-- Message Types Statistics -->
        <?php if ($stats['total'] > 0): ?>
        <div class="stats">
            <h3>📈 Message Types</h3>
            <?php foreach ($stats['by_type'] as $type => $count): ?>
                <p><strong><?= ucfirst($type) ?>:</strong> <?= $count ?></p>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- Features List -->
        <div class="stats">
            <h3>✨ Available Features</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; text-align: left;">
                <div>
                    <strong>🍞 Toast Messages:</strong><br>
                    • Success, Error, Warning, Info<br>
                    • Achievement, Custom Types
                </div>
                <div>
                    <strong>🪟 Modal Dialogs:</strong><br>
                    • Alert, Modal, Popup<br>
                    • Confirmation, Input Prompt
                </div>
                <div>
                    <strong>📊 Progress & Loading:</strong><br>
                    • Progress Bars<br>
                    • Loading Spinners
                </div>
                <div>
                    <strong>🎨 Themes & Animations:</strong><br>
                    • Modern, Glass, Neon, Minimal<br>
                    • Dark, Light Themes<br>
                    • Slide, Fade, Bounce, Zoom, Flip
                </div>
                <div>
                    <strong>📱 Mobile Features:</strong><br>
                    • Responsive Design<br>
                    • Touch Gestures<br>
                    • Safe Area Support
                </div>
                <div>
                    <strong>⚙️ Advanced Options:</strong><br>
                    • Custom Positioning<br>
                    • Auto-hide Control<br>
                    • Sound Effects<br>
                    • Keyboard Shortcuts
                </div>
            </div>
        </div>

        <!-- Usage Instructions -->
        <div class="stats">
            <h3>🎯 Quick Start Guide</h3>
            <div style="text-align: left;">
                <p><strong>1. Include the file:</strong></p>
                <code style="background: rgba(0,0,0,0.2); padding: 5px; border-radius: 3px; display: block; margin: 5px 0;">
                    &lt;?php require_once 'ultimate-flash.php'; ?&gt;
                </code>

                <p><strong>2. Add messages anywhere:</strong></p>
                <code style="background: rgba(0,0,0,0.2); padding: 5px; border-radius: 3px; display: block; margin: 5px 0;">
                    flash_success('Data saved!');<br>
                    flash_alert('Important notice!');<br>
                    flash_confirm('Delete item?');
                </code>

                <p><strong>3. Display messages:</strong></p>
                <code style="background: rgba(0,0,0,0.2); padding: 5px; border-radius: 3px; display: block; margin: 5px 0;">
                    &lt;?= flash_render() ?&gt;
                </code>

                <p><strong>That's it!</strong> No configuration, no dependencies, no setup required!</p>
            </div>
        </div>
    </div>
    
    <!-- IMPORTANT: Render flash messages -->
    <?= flash_render() ?>
    
    <script>
        function changeTheme(theme) {
            // This would require a page reload to change PHP settings
            // For demo purposes, we'll just show an alert
            alert('Theme changed to: ' + theme + '\nReload the page to see the change.');
        }
        
        function changePosition(position) {
            // This would require a page reload to change PHP settings
            // For demo purposes, we'll just show an alert
            alert('Position changed to: ' + position + '\nReload the page to see the change.');
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        document.querySelector('input[value="success"]').closest('form').submit();
                        break;
                    case '2':
                        e.preventDefault();
                        document.querySelector('input[value="error"]').closest('form').submit();
                        break;
                    case '3':
                        e.preventDefault();
                        document.querySelector('input[value="warning"]').closest('form').submit();
                        break;
                    case '4':
                        e.preventDefault();
                        document.querySelector('input[value="info"]').closest('form').submit();
                        break;
                }
            }
        });
        
        // Callback functions for testing
        window.handleConfirm = function() {
            alert('Confirmed! This would execute your confirm action.');
        };

        window.handleCancel = function() {
            alert('Cancelled! This would execute your cancel action.');
        };

        window.handlePromptSubmit = function(value) {
            alert('You entered: ' + value);
        };

        console.log('🚀 Ultimate Flash Messages Test Page Loaded');
        console.log('⌨️ Keyboard Shortcuts: Ctrl+1,2,3,4 for different message types');
        console.log('📁 Only one file needed: ultimate-flash.php');
        console.log('🎯 Features Available:');
        console.log('   - Toasts, Alerts, Modals, Confirmations');
        console.log('   - Progress Bars, Loading Spinners');
        console.log('   - Input Prompts, Notifications');
        console.log('   - Custom Types, Tooltips');
        console.log('   - Multiple Themes & Animations');
    </script>
</body>
</html>
