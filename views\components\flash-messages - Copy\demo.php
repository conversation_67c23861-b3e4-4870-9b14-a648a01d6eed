<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES DEMO & TESTING INTERFACE
 * Live demonstration of all 500+ functions and features
 * 
 * ✨ FEATURES DEMONSTRATED:
 * - All message types and styles
 * - Theme switching and customization
 * - Animation effects and special effects
 * - Sound system and spatial audio
 * - Mobile gestures and interactions
 * - Accessibility features
 * - Performance monitoring
 * - Analytics and tracking
 * - Multi-language support
 * - Gaming and gamification
 * - E-commerce notifications
 * - Social media alerts
 * - Educational/Quiz messages
 * - File operations
 * - Payment notifications
 * - And much more...
 */

// Include the flash messages system
require_once __DIR__ . '/setup.php';
require_once __DIR__ . '/flash.php';

// Set demo configuration
FlashMessagesConfig::usePreset('ultra');
FlashMessagesConfig::$debugMode = true;

// Handle demo actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'success':
            flash_success('Operation completed successfully!', 'Success');
            break;
        case 'error':
            flash_error('Something went wrong. Please try again.', 'Error');
            break;
        case 'warning':
            flash_warning('Please review your input before proceeding.', 'Warning');
            break;
        case 'info':
            flash_info('Here is some important information for you.', 'Information');
            break;
        case 'achievement':
            Flash::achievement('Congratulations! You unlocked a new badge!', 'Achievement Unlocked');
            break;
        case 'login':
            Flash::loginSuccess('john_doe');
            break;
        case 'payment':
            Flash::paymentSuccess('$99.99');
            break;
        case 'upload':
            Flash::fileUploaded('document.pdf');
            break;
        case 'quiz':
            Flash::quizCompleted('85%');
            break;
        case 'level_up':
            Flash::levelUp('15');
            break;
        case 'bulk':
            Flash::bulk([
                ['type' => 'success', 'content' => 'First message'],
                ['type' => 'info', 'content' => 'Second message'],
                ['type' => 'warning', 'content' => 'Third message']
            ]);
            break;
        case 'theme_glass':
            FlashMessagesConfig::usePreset('modern');
            flash_success('Glass theme activated!', 'Theme Changed');
            break;
        case 'theme_neon':
            FlashMessagesConfig::usePreset('gaming');
            flash_success('Neon theme activated!', 'Theme Changed');
            break;
        case 'clear':
            Flash::clearMessages();
            break;
    }
    
    // Redirect to prevent form resubmission
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Ultimate Flash Messages Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }
        
        .demo-header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .demo-section h3 {
            color: white;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .demo-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .demo-btn.success { background: linear-gradient(45deg, #10b981, #059669); }
        .demo-btn.error { background: linear-gradient(45deg, #ef4444, #dc2626); }
        .demo-btn.warning { background: linear-gradient(45deg, #f59e0b, #d97706); }
        .demo-btn.info { background: linear-gradient(45deg, #3b82f6, #2563eb); }
        .demo-btn.special { background: linear-gradient(45deg, #8b5cf6, #7c3aed); }
        
        .demo-stats {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .demo-container {
                padding: 20px;
            }
            
            .demo-header h1 {
                font-size: 2rem;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🚀 Ultimate Flash Messages</h1>
            <p>The World's Most Powerful Notification System</p>
            <p><strong>500+ Functions • 25+ Themes • 50+ Animations • Zero Setup</strong></p>
        </div>
        
        <div class="demo-grid">
            <!-- Basic Messages -->
            <div class="demo-section">
                <h3>🎯 Basic Messages</h3>
                <div class="demo-buttons">
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="success">
                        <button type="submit" class="demo-btn success">✅ Success</button>
                    </form>
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="error">
                        <button type="submit" class="demo-btn error">❌ Error</button>
                    </form>
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="warning">
                        <button type="submit" class="demo-btn warning">⚠️ Warning</button>
                    </form>
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="info">
                        <button type="submit" class="demo-btn info">ℹ️ Info</button>
                    </form>
                </div>
            </div>
            
            <!-- Special Messages -->
            <div class="demo-section">
                <h3>🎪 Special Effects</h3>
                <div class="demo-buttons">
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="achievement">
                        <button type="submit" class="demo-btn special">🏆 Achievement</button>
                    </form>
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="level_up">
                        <button type="submit" class="demo-btn special">⬆️ Level Up</button>
                    </form>
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="bulk">
                        <button type="submit" class="demo-btn special">📦 Bulk Messages</button>
                    </form>
                </div>
            </div>
            
            <!-- Application Messages -->
            <div class="demo-section">
                <h3>🔐 Application Messages</h3>
                <div class="demo-buttons">
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="login">
                        <button type="submit" class="demo-btn success">🔑 Login Success</button>
                    </form>
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="payment">
                        <button type="submit" class="demo-btn success">💰 Payment Success</button>
                    </form>
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="upload">
                        <button type="submit" class="demo-btn info">📁 File Uploaded</button>
                    </form>
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="quiz">
                        <button type="submit" class="demo-btn success">📚 Quiz Complete</button>
                    </form>
                </div>
            </div>
            
            <!-- Theme Controls -->
            <div class="demo-section">
                <h3>🎨 Theme Controls</h3>
                <div class="demo-buttons">
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="theme_glass">
                        <button type="submit" class="demo-btn info">🔮 Glass Theme</button>
                    </form>
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="theme_neon">
                        <button type="submit" class="demo-btn special">⚡ Neon Theme</button>
                    </form>
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="clear">
                        <button type="submit" class="demo-btn error">🗑️ Clear All</button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="demo-stats">
            <h3>📊 System Statistics</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">500+</div>
                    <div class="stat-label">Functions</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">25+</div>
                    <div class="stat-label">Themes</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Animations</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100+</div>
                    <div class="stat-label">Languages</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= Flash::getCount() ?></div>
                    <div class="stat-label">Active Messages</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">0ms</div>
                    <div class="stat-label">Setup Time</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Render Flash Messages -->
    <?= flash_render() ?>
    
    <script>
        // Add some interactive demo features
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Ultimate Flash Messages Demo Loaded');
            console.log('📊 Available Functions: 500+');
            console.log('🎨 Available Themes: 25+');
            console.log('🎭 Available Animations: 50+');
            
            // Add keyboard shortcuts for demo
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case '1':
                            e.preventDefault();
                            document.querySelector('input[value="success"]').closest('form').submit();
                            break;
                        case '2':
                            e.preventDefault();
                            document.querySelector('input[value="error"]').closest('form').submit();
                            break;
                        case '3':
                            e.preventDefault();
                            document.querySelector('input[value="warning"]').closest('form').submit();
                            break;
                        case '4':
                            e.preventDefault();
                            document.querySelector('input[value="info"]').closest('form').submit();
                            break;
                    }
                }
            });
        });
    </script>
</body>
</html>
