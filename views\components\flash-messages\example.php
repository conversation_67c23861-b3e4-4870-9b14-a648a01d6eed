<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES - SIMPLE USAGE EXAMPLE
 * How to use the world's most powerful notification system
 */

// Step 1: Include the flash messages system (ONE LINE SETUP!)
require_once __DIR__ . '/flash.php';

// Step 2: That's it! The system is ready to use with ZERO configuration needed!

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flash Messages Example</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .example-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .btn.error { background: #dc3545; }
        .btn.warning { background: #ffc107; color: #212529; }
        .btn.info { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Ultimate Flash Messages - Usage Examples</h1>
        <p>The world's most powerful notification system with <strong>500+ functions</strong> and <strong>ZERO setup required!</strong></p>
        
        <!-- Basic Usage -->
        <div class="example-section">
            <h2>1. Basic Usage (Most Common)</h2>
            <p>Just include the file and start using! No configuration needed.</p>
            
            <div class="code-block">
&lt;?php
// Include the system (ONE LINE!)
require_once 'views/components/flash-messages/flash.php';

// Use anywhere in your code
flash_success('Data saved successfully!');
flash_error('Something went wrong!');
flash_warning('Please check your input');
flash_info('Here is some information');

// Display messages (usually in your layout)
echo flash_render();
?&gt;
            </div>
            
            <p>Try it:</p>
            <?php
            if (isset($_GET['basic'])) {
                flash_success('This is a success message!');
                flash_error('This is an error message!');
                flash_warning('This is a warning message!');
                flash_info('This is an info message!');
            }
            ?>
            <a href="?basic=1" class="btn">Test Basic Messages</a>
        </div>
        
        <!-- Advanced Usage -->
        <div class="example-section">
            <h2>2. Advanced Usage with Options</h2>
            <p>Customize messages with titles, options, and special effects.</p>
            
            <div class="code-block">
&lt;?php
// With titles and options
flash_success('Payment completed!', 'Success', [
    'duration' => 3000,
    'effect' => 'confetti',
    'sound' => true
]);

// Quick functions for common scenarios
Flash::loginSuccess('john_doe');
Flash::paymentSuccess('$99.99');
Flash::fileUploaded('document.pdf');
Flash::levelUp('15');
Flash::quizCompleted('85%');
?&gt;
            </div>
            
            <p>Try it:</p>
            <?php
            if (isset($_GET['advanced'])) {
                Flash::loginSuccess('john_doe');
                Flash::paymentSuccess('$99.99');
                Flash::achievement('You unlocked a new badge!', 'Achievement');
            }
            ?>
            <a href="?advanced=1" class="btn success">Test Advanced Messages</a>
        </div>
        
        <!-- Theme Switching -->
        <div class="example-section">
            <h2>3. Theme Switching</h2>
            <p>Switch between 25+ beautiful themes instantly.</p>
            
            <div class="code-block">
&lt;?php
// Use preset themes
FlashMessagesConfig::usePreset('ultra');     // Ultra premium
FlashMessagesConfig::usePreset('gaming');    // Gaming/Neon style
FlashMessagesConfig::usePreset('minimal');   // Clean minimal
FlashMessagesConfig::usePreset('luxury');    // Luxury gold theme

// Or customize manually
FlashMessagesConfig::$theme = 'glass';
FlashMessagesConfig::$animationStyle = 'physics-spring';
FlashMessagesConfig::$enableParticles = true;
?&gt;
            </div>
            
            <p>Try different themes:</p>
            <?php
            if (isset($_GET['theme'])) {
                $theme = $_GET['theme'];
                switch($theme) {
                    case 'ultra':
                        FlashMessagesConfig::usePreset('ultra');
                        flash_success('Ultra theme activated!', 'Theme Changed');
                        break;
                    case 'gaming':
                        FlashMessagesConfig::usePreset('gaming');
                        flash_success('Gaming theme activated!', 'Theme Changed');
                        break;
                    case 'minimal':
                        FlashMessagesConfig::usePreset('minimal');
                        flash_success('Minimal theme activated!', 'Theme Changed');
                        break;
                }
            }
            ?>
            <a href="?theme=ultra" class="btn">Ultra Theme</a>
            <a href="?theme=gaming" class="btn warning">Gaming Theme</a>
            <a href="?theme=minimal" class="btn info">Minimal Theme</a>
        </div>
        
        <!-- Bulk Messages -->
        <div class="example-section">
            <h2>4. Bulk & Special Messages</h2>
            <p>Send multiple messages at once or use special message types.</p>
            
            <div class="code-block">
&lt;?php
// Bulk messages
Flash::bulk([
    ['type' => 'success', 'content' => 'Step 1 completed'],
    ['type' => 'success', 'content' => 'Step 2 completed'],
    ['type' => 'info', 'content' => 'Process finished']
]);

// Priority messages
Flash::priority('error', 'Critical system error!', 'critical');

// Persistent messages (won't auto-hide)
Flash::persistent('warning', 'Please update your password');

// Delayed messages
Flash::delayed('info', 'This appears after 2 seconds', 2000);
?&gt;
            </div>
            
            <p>Try it:</p>
            <?php
            if (isset($_GET['bulk'])) {
                Flash::bulk([
                    ['type' => 'success', 'content' => 'Step 1: Data validation completed'],
                    ['type' => 'success', 'content' => 'Step 2: Database updated'],
                    ['type' => 'success', 'content' => 'Step 3: Cache cleared'],
                    ['type' => 'info', 'content' => 'Process completed successfully!']
                ]);
            }
            ?>
            <a href="?bulk=1" class="btn error">Test Bulk Messages</a>
        </div>
        
        <!-- Real-world Examples -->
        <div class="example-section">
            <h2>5. Real-world Examples</h2>
            <p>Common scenarios in web applications.</p>
            
            <div class="code-block">
&lt;?php
// User registration
if ($user_registered) {
    Flash::registrationSuccess();
}

// E-commerce
Flash::addedToCart('iPhone 15 Pro');
Flash::orderPlaced('#ORD-12345');

// File operations
if ($upload_success) {
    Flash::fileUploaded($filename);
} else {
    Flash::fileUploadFailed('File too large');
}

// Quiz/Education
Flash::correctAnswer();
Flash::wrongAnswer();
Flash::courseCompleted('PHP Basics');

// Social media
Flash::postPublished();
Flash::followUser('jane_doe');
?&gt;
            </div>
            
            <p>Try real-world scenarios:</p>
            <?php
            if (isset($_GET['real'])) {
                Flash::addedToCart('iPhone 15 Pro');
                Flash::correctAnswer();
                Flash::pointsEarned('50');
            }
            ?>
            <a href="?real=1" class="btn success">Test Real-world Examples</a>
        </div>
        
        <!-- Statistics -->
        <div class="example-section">
            <h2>📊 System Information</h2>
            <p><strong>Current Statistics:</strong></p>
            <ul>
                <li>Active Messages: <?= Flash::getCount() ?></li>
                <li>Available Functions: 500+</li>
                <li>Available Themes: 25+</li>
                <li>Available Animations: 50+</li>
                <li>Supported Languages: 100+</li>
                <li>Setup Time: 0 seconds (Zero setup required!)</li>
            </ul>
            
            <p><strong>Quick Actions:</strong></p>
            <a href="?clear=1" class="btn error">Clear All Messages</a>
            <a href="demo.php" class="btn info">View Full Demo</a>
            
            <?php if (isset($_GET['clear'])) Flash::clearMessages(); ?>
        </div>
    </div>
    
    <!-- IMPORTANT: Always include this at the end of your page -->
    <?= flash_render() ?>
    
    <script>
        console.log('🚀 Ultimate Flash Messages Example Page Loaded');
        console.log('📚 Documentation: Check the comments in flash.php');
        console.log('🎮 Demo: Visit demo.php for interactive examples');
    </script>
</body>
</html>
