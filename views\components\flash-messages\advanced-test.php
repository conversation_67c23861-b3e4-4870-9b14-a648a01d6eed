<?php
/**
 * 🚀 ADVANCED TEST PAGE - Ultimate Flash Messages v2.0
 * Complete testing interface with settings customization
 */

// Include the ultimate flash system
require_once __DIR__ . '/ultimate-flash.php';

// Handle AJAX requests for settings
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax'])) {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    $response = ['success' => false, 'message' => ''];
    
    switch ($action) {
        case 'change_theme':
            UltimateFlash::setTheme($_POST['theme']);
            $response = ['success' => true, 'message' => 'Theme changed to ' . $_POST['theme']];
            break;
            
        case 'change_position':
            UltimateFlash::setPosition($_POST['position']);
            $response = ['success' => true, 'message' => 'Position changed to ' . $_POST['position']];
            break;
            
        case 'change_animation':
            UltimateFlash::$animationStyle = $_POST['animation'];
            $response = ['success' => true, 'message' => 'Animation changed to ' . $_POST['animation']];
            break;
            
        case 'change_duration':
            UltimateFlash::$duration = (int)$_POST['duration'];
            $response = ['success' => true, 'message' => 'Duration changed to ' . $_POST['duration'] . 'ms'];
            break;
    }
    
    echo json_encode($response);
    exit;
}

// Handle form submissions for testing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['ajax'])) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        // Gaming Functions
        case 'quiz_victory':
            UltimateFlash::quizVictory(8, 10, 80);
            break;
        case 'perfect_score':
            UltimateFlash::perfectScore('Mathematics');
            break;
        case 'high_score':
            UltimateFlash::highScore(9500, 8200);
            break;
        case 'daily_coins':
            UltimateFlash::dailyCoins(150);
            break;
        case 'streak':
            UltimateFlash::streakAchievement(7, 'login');
            break;
        case 'badge':
            UltimateFlash::badgeUnlocked('Quiz Master', 'Complete 10 quizzes with 90%+ score');
            break;
        case 'rank_up':
            UltimateFlash::rankPromotion('Gold Member', 'Silver Member');
            break;
        case 'milestone':
            UltimateFlash::milestone('1000 Points', '1000/1000', '2000 Points');
            break;
            
        // Social Functions
        case 'welcome_back':
            UltimateFlash::welcomeBack('John Doe', '2 hours ago');
            break;
        case 'new_follower':
            UltimateFlash::newFollower('Jane Smith');
            break;
        case 'post_liked':
            UltimateFlash::postLiked('Mike Johnson', 'My awesome post');
            break;
        case 'new_comment':
            UltimateFlash::newComment('Sarah Wilson', 'Learning PHP');
            break;
        case 'viral_post':
            UltimateFlash::viralPost('Amazing tutorial!', 1500, 250);
            break;
        case 'top_creator':
            UltimateFlash::topCreator('this month');
            break;
            
        // E-commerce Functions
        case 'add_to_cart':
            UltimateFlash::itemAddedToCart('Wireless Headphones', '$99.99', 1);
            break;
        case 'order_confirmed':
            UltimateFlash::orderConfirmed('#ORD-12345', '$299.99', '3 items');
            break;
        case 'order_shipped':
            UltimateFlash::orderShipped('#ORD-12345', 'TRK-789456');
            break;
        case 'order_delivered':
            UltimateFlash::orderDelivered('#ORD-12345');
            break;
        case 'discount':
            UltimateFlash::discountApplied('20% OFF', 'SAVE20');
            break;
        case 'flash_sale':
            UltimateFlash::flashSale('50% OFF', '2 hours');
            break;
            
        // Freelance Functions
        case 'new_job':
            UltimateFlash::newJobPosted('Website Development', '$500-1000', 'TechCorp');
            break;
        case 'job_awarded':
            UltimateFlash::jobAwarded('Mobile App Design', '$750', 'StartupXYZ');
            break;
        case 'project_completed':
            UltimateFlash::projectCompleted('E-commerce Website', '$1200');
            break;
        case 'payment_received':
            UltimateFlash::paymentReceived('$750', 'StartupXYZ', 'Mobile App Design');
            break;
        case 'client_review':
            UltimateFlash::clientReview(5, 'TechCorp', 'Excellent work, highly recommended!');
            break;
            
        // Learning Functions
        case 'course_enrolled':
            UltimateFlash::courseEnrolled('Advanced PHP Development', 'Dr. Smith');
            break;
        case 'course_completed':
            UltimateFlash::courseCompleted('JavaScript Fundamentals', 'A+', true);
            break;
        case 'skill_mastered':
            UltimateFlash::skillMastered('React.js', 'Expert');
            break;
        case 'certificate':
            UltimateFlash::certificateEarned('Full Stack Developer', 'CodeAcademy');
            break;
        case 'learning_goal':
            UltimateFlash::learningGoalAchieved('Master PHP OOP', '40 hours');
            break;
            
        // Basic Functions
        case 'success':
            flash_success('Operation completed successfully!');
            break;
        case 'error':
            flash_error('Something went wrong. Please try again.');
            break;
        case 'warning':
            flash_warning('Please check your input before proceeding.');
            break;
        case 'info':
            flash_info('Here is some important information for you.');
            break;
    }
    
    // Redirect to prevent form resubmission
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultimate Flash Messages - Advanced Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            color: white;
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .settings-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        
        .setting-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .setting-group label {
            font-weight: 600;
            font-size: 14px;
        }
        
        .setting-group select,
        .setting-group input[type="range"] {
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
        }
        
        .setting-group select option {
            background: #333;
            color: white;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
        }
        
        .test-section h4 {
            margin-bottom: 15px;
            font-size: 1.2rem;
            text-align: center;
        }
        
        .test-btn {
            width: 100%;
            padding: 12px 16px;
            margin: 5px 0;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .test-btn.success { background: linear-gradient(45deg, #10b981, #059669); }
        .test-btn.error { background: linear-gradient(45deg, #ef4444, #dc2626); }
        .test-btn.warning { background: linear-gradient(45deg, #f59e0b, #d97706); }
        .test-btn.info { background: linear-gradient(45deg, #3b82f6, #2563eb); }
        .test-btn.achievement { background: linear-gradient(45deg, #8b5cf6, #7c3aed); }
        
        .stats {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .settings-grid {
                grid-template-columns: 1fr;
            }
            
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Ultimate Flash Messages v2.0</h1>
        <p style="text-align: center; margin-bottom: 30px; opacity: 0.9;">
            Advanced Testing Interface with Real-time Customization
        </p>

        <!-- Settings Panel -->
        <div class="settings-panel">
            <h3>⚙️ Real-time Settings</h3>
            <div class="settings-grid">
                <div class="setting-group">
                    <label>🎨 Theme:</label>
                    <select id="themeSelect" onchange="changeTheme()">
                        <option value="modern">Modern</option>
                        <option value="glass">Glass</option>
                        <option value="neon">Neon</option>
                        <option value="minimal">Minimal</option>
                        <option value="dark">Dark</option>
                        <option value="light">Light</option>
                    </select>
                </div>

                <div class="setting-group">
                    <label>📍 Position:</label>
                    <select id="positionSelect" onchange="changePosition()">
                        <option value="top-right">Top Right</option>
                        <option value="top-center">Top Center</option>
                        <option value="top-left">Top Left</option>
                        <option value="bottom-right">Bottom Right</option>
                        <option value="bottom-center">Bottom Center</option>
                        <option value="bottom-left">Bottom Left</option>
                        <option value="center">Center</option>
                    </select>
                </div>

                <div class="setting-group">
                    <label>🎬 Animation:</label>
                    <select id="animationSelect" onchange="changeAnimation()">
                        <option value="slide">Slide</option>
                        <option value="fade">Fade</option>
                        <option value="bounce">Bounce</option>
                        <option value="zoom">Zoom</option>
                        <option value="flip">Flip</option>
                    </select>
                </div>

                <div class="setting-group">
                    <label>⏱️ Duration:</label>
                    <input type="range" id="durationRange" min="1000" max="10000" value="5000" onchange="changeDuration()">
                    <span id="durationValue">5s</span>
                </div>

                <div class="setting-group">
                    <label>🔊 Sound:</label>
                    <input type="checkbox" id="soundToggle" onchange="toggleSound()" checked>
                </div>

                <div class="setting-group">
                    <label>📊 Progress:</label>
                    <input type="checkbox" id="progressToggle" onchange="toggleProgress()" checked>
                </div>
            </div>
        </div>

        <!-- Test Buttons Grid -->
        <div class="test-grid">
            <!-- Gaming Functions -->
            <div class="test-section">
                <h4>🎮 Gaming & Achievements</h4>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="quiz_victory">
                    <button type="submit" class="test-btn achievement">🎉 Quiz Victory</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="perfect_score">
                    <button type="submit" class="test-btn achievement">🌟 Perfect Score</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="high_score">
                    <button type="submit" class="test-btn achievement">🚀 High Score</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="daily_coins">
                    <button type="submit" class="test-btn success">🪙 Daily Coins</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="streak">
                    <button type="submit" class="test-btn achievement">🔥 Streak</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="badge">
                    <button type="submit" class="test-btn achievement">🎖️ Badge</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="rank_up">
                    <button type="submit" class="test-btn achievement">👑 Rank Up</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="milestone">
                    <button type="submit" class="test-btn achievement">🎊 Milestone</button>
                </form>
            </div>

            <!-- Social Functions -->
            <div class="test-section">
                <h4>👥 Social Media</h4>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="welcome_back">
                    <button type="submit" class="test-btn success">👋 Welcome Back</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="new_follower">
                    <button type="submit" class="test-btn info">👥 New Follower</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="post_liked">
                    <button type="submit" class="test-btn info">❤️ Post Liked</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="new_comment">
                    <button type="submit" class="test-btn info">💬 New Comment</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="viral_post">
                    <button type="submit" class="test-btn achievement">🎉 Viral Post</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="top_creator">
                    <button type="submit" class="test-btn achievement">🏆 Top Creator</button>
                </form>
            </div>

            <!-- E-commerce Functions -->
            <div class="test-section">
                <h4>🛒 E-commerce</h4>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="add_to_cart">
                    <button type="submit" class="test-btn success">🛒 Add to Cart</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="order_confirmed">
                    <button type="submit" class="test-btn success">✅ Order Confirmed</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="order_shipped">
                    <button type="submit" class="test-btn info">🚚 Order Shipped</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="order_delivered">
                    <button type="submit" class="test-btn success">📦 Delivered</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="discount">
                    <button type="submit" class="test-btn success">🎁 Discount</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="flash_sale">
                    <button type="submit" class="test-btn warning">🔥 Flash Sale</button>
                </form>
            </div>

            <!-- Freelance Functions -->
            <div class="test-section">
                <h4>💼 Freelance & Jobs</h4>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="new_job">
                    <button type="submit" class="test-btn info">💼 New Job</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="job_awarded">
                    <button type="submit" class="test-btn achievement">🎉 Job Awarded</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="project_completed">
                    <button type="submit" class="test-btn success">✅ Project Done</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="payment_received">
                    <button type="submit" class="test-btn success">💰 Payment</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="client_review">
                    <button type="submit" class="test-btn success">⭐ Review</button>
                </form>
            </div>

            <!-- Learning Functions -->
            <div class="test-section">
                <h4>📚 Learning & Education</h4>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="course_enrolled">
                    <button type="submit" class="test-btn success">📚 Course Enrolled</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="course_completed">
                    <button type="submit" class="test-btn achievement">🎓 Course Complete</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="skill_mastered">
                    <button type="submit" class="test-btn achievement">🧠 Skill Mastered</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="certificate">
                    <button type="submit" class="test-btn achievement">📜 Certificate</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="learning_goal">
                    <button type="submit" class="test-btn achievement">🎯 Goal Achieved</button>
                </form>
            </div>

            <!-- Basic Functions -->
            <div class="test-section">
                <h4>📢 Basic Messages</h4>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="success">
                    <button type="submit" class="test-btn success">✅ Success</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="error">
                    <button type="submit" class="test-btn error">❌ Error</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="warning">
                    <button type="submit" class="test-btn warning">⚠️ Warning</button>
                </form>
                <form method="post" style="display: contents;">
                    <input type="hidden" name="action" value="info">
                    <button type="submit" class="test-btn info">ℹ️ Info</button>
                </form>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats">
            <h3>📊 System Statistics</h3>
            <?php $stats = UltimateFlash::getStats(); ?>
            <p><strong>Total Messages:</strong> <?= $stats['total'] ?></p>
            <p><strong>Success:</strong> <?= $stats['success'] ?> | <strong>Error:</strong> <?= $stats['error'] ?> | <strong>Warning:</strong> <?= $stats['warning'] ?> | <strong>Info:</strong> <?= $stats['info'] ?></p>
            <p><strong>Current Theme:</strong> <?= UltimateFlash::$theme ?> | <strong>Position:</strong> <?= UltimateFlash::$position ?></p>
        </div>
    </div>

    <!-- Render Flash Messages -->
    <?= UltimateFlash::render() ?>

    <script>
        // Settings Functions
        function changeTheme() {
            const theme = document.getElementById('themeSelect').value;
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `ajax=1&action=change_theme&theme=${theme}`
            }).then(() => location.reload());
        }

        function changePosition() {
            const position = document.getElementById('positionSelect').value;
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `ajax=1&action=change_position&position=${position}`
            }).then(() => location.reload());
        }

        function changeAnimation() {
            const animation = document.getElementById('animationSelect').value;
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `ajax=1&action=change_animation&animation=${animation}`
            }).then(() => location.reload());
        }

        function changeDuration() {
            const duration = document.getElementById('durationRange').value;
            document.getElementById('durationValue').textContent = (duration / 1000) + 's';
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `ajax=1&action=change_duration&duration=${duration}`
            });
        }

        function toggleSound() {
            const enabled = document.getElementById('soundToggle').checked;
            console.log('Sound ' + (enabled ? 'enabled' : 'disabled'));
        }

        function toggleProgress() {
            const enabled = document.getElementById('progressToggle').checked;
            console.log('Progress ' + (enabled ? 'enabled' : 'disabled'));
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Ultimate Flash Messages v2.0 - Advanced Test Interface Loaded');
        });
    </script>
</body>
</html>
