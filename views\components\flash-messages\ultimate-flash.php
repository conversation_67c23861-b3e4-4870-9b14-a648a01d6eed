<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES SYSTEM - SINGLE FILE VERSION
 * The World's Most Powerful Notification System in ONE FILE
 * 
 * ✨ FEATURES:
 * - 100+ Built-in Functions
 * - Beautiful Themes & Animations
 * - Zero Setup Required
 * - Works Instantly
 * - Mobile Responsive
 * - Cross-browser Compatible
 * 
 * @version 2.0.0
 * <AUTHOR> Team
 */

// Start session if not started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize flash messages array
if (!isset($_SESSION['flash_messages'])) {
    $_SESSION['flash_messages'] = [];
}

/**
 * 🎯 CORE FLASH MESSAGE CLASS
 */
class UltimateFlash {
    
    // Configuration
    public static $theme = 'modern'; // modern, glass, neon, minimal, dark, light
    public static $position = 'top-right'; // top-right, top-left, bottom-right, bottom-left, top-center, bottom-center, center
    public static $autoHide = true;
    public static $duration = 5000; // milliseconds
    public static $showProgress = true;
    public static $enableSounds = false;
    public static $clickToDismiss = true;
    public static $maxMessages = 5;
    public static $enableAnimations = true;
    public static $animationStyle = 'slide'; // slide, fade, bounce, zoom, flip
    public static $enableBackdrop = true; // for modals
    public static $backdropColor = 'rgba(0,0,0,0.5)';
    public static $enableConfirmations = true;
    public static $confirmButtonText = 'Confirm';
    public static $cancelButtonText = 'Cancel';
    public static $enableProgressBars = true;
    public static $enableCustomIcons = true;
    
    /**
     * ✅ Success Message
     */
    public static function success($message, $title = 'Success', $options = []) {
        return self::add('success', $message, $title, $options);
    }
    
    /**
     * ❌ Error Message
     */
    public static function error($message, $title = 'Error', $options = []) {
        return self::add('error', $message, $title, $options);
    }
    
    /**
     * ⚠️ Warning Message
     */
    public static function warning($message, $title = 'Warning', $options = []) {
        return self::add('warning', $message, $title, $options);
    }
    
    /**
     * ℹ️ Info Message
     */
    public static function info($message, $title = 'Info', $options = []) {
        return self::add('info', $message, $title, $options);
    }
    
    /**
     * 🎉 Achievement Message
     */
    public static function achievement($message, $title = 'Achievement Unlocked!', $options = []) {
        $options['effect'] = 'confetti';
        return self::add('achievement', $message, $title, $options);
    }

    /**
     * 🚨 Alert Dialog
     */
    public static function alert($message, $title = 'Alert', $options = []) {
        $options['type'] = 'alert';
        $options['backdrop'] = true;
        $options['dismissible'] = true;
        return self::add('alert', $message, $title, $options);
    }

    /**
     * 📋 Modal Dialog
     */
    public static function modal($message, $title = 'Modal', $options = []) {
        $options['type'] = 'modal';
        $options['backdrop'] = true;
        $options['dismissible'] = true;
        $options['size'] = $options['size'] ?? 'medium'; // small, medium, large, fullscreen
        return self::add('modal', $message, $title, $options);
    }

    /**
     * 💬 Popup Message
     */
    public static function popup($message, $title = 'Popup', $options = []) {
        $options['type'] = 'popup';
        $options['backdrop'] = false;
        $options['position'] = $options['position'] ?? 'center';
        return self::add('popup', $message, $title, $options);
    }

    /**
     * ❓ Confirmation Dialog
     */
    public static function confirm($message, $title = 'Confirm', $onConfirm = '', $onCancel = '', $options = []) {
        $options['type'] = 'confirm';
        $options['backdrop'] = true;
        $options['dismissible'] = false;
        $options['onConfirm'] = $onConfirm;
        $options['onCancel'] = $onCancel;
        $options['confirmText'] = $options['confirmText'] ?? self::$confirmButtonText;
        $options['cancelText'] = $options['cancelText'] ?? self::$cancelButtonText;
        return self::add('confirm', $message, $title, $options);
    }

    /**
     * 📊 Progress Indicator
     */
    public static function progress($message, $title = 'Loading...', $percentage = 0, $options = []) {
        $options['type'] = 'progress';
        $options['percentage'] = $percentage;
        $options['backdrop'] = $options['backdrop'] ?? true;
        $options['dismissible'] = false;
        $options['showPercentage'] = $options['showPercentage'] ?? true;
        return self::add('progress', $message, $title, $options);
    }

    /**
     * ⏳ Loading Spinner
     */
    public static function loading($message = 'Loading...', $title = '', $options = []) {
        $options['type'] = 'loading';
        $options['backdrop'] = true;
        $options['dismissible'] = false;
        $options['spinner'] = $options['spinner'] ?? 'default'; // default, dots, pulse, bars
        return self::add('loading', $message, $title, $options);
    }

    /**
     * 🎨 Custom Message
     */
    public static function custom($type, $message, $title = '', $options = []) {
        $options['type'] = 'custom';
        $options['customType'] = $type;
        $options['customIcon'] = $options['customIcon'] ?? '';
        $options['customColor'] = $options['customColor'] ?? '';
        return self::add('custom', $message, $title, $options);
    }

    /**
     * 🔔 Notification (Persistent)
     */
    public static function notification($message, $title = 'Notification', $options = []) {
        $options['type'] = 'notification';
        $options['persistent'] = true;
        $options['dismissible'] = true;
        $options['sound'] = $options['sound'] ?? true;
        return self::add('notification', $message, $title, $options);
    }

    /**
     * 💡 Tooltip
     */
    public static function tooltip($message, $element = '', $options = []) {
        $options['type'] = 'tooltip';
        $options['target'] = $element;
        $options['position'] = $options['position'] ?? 'top'; // top, bottom, left, right
        $options['dismissible'] = false;
        return self::add('tooltip', $message, '', $options);
    }
    
    /**
     * 🔑 Login Success
     */
    public static function loginSuccess($username = '') {
        return self::success("Welcome back" . ($username ? ", {$username}" : "") . "!", 'Login Successful');
    }
    
    /**
     * 🔑 Login Failed
     */
    public static function loginFailed($reason = '') {
        return self::error("Login failed" . ($reason ? ": {$reason}" : ""), 'Login Failed');
    }
    
    /**
     * 🚪 Logout Success
     */
    public static function logout() {
        return self::info("You have been logged out successfully", 'Goodbye!');
    }
    
    /**
     * 📝 Registration Success
     */
    public static function registrationSuccess() {
        return self::achievement("Account created successfully! Welcome aboard!", 'Registration Complete');
    }
    
    /**
     * 💰 Payment Success
     */
    public static function paymentSuccess($amount = '') {
        return self::success("Payment processed successfully" . ($amount ? " for {$amount}" : ""), 'Payment Complete');
    }
    
    /**
     * 💰 Payment Failed
     */
    public static function paymentFailed($reason = '') {
        return self::error("Payment failed" . ($reason ? ": {$reason}" : ""), 'Payment Error');
    }
    
    /**
     * 📁 File Uploaded
     */
    public static function fileUploaded($filename = '') {
        return self::success("File uploaded successfully" . ($filename ? ": {$filename}" : ""), 'Upload Complete');
    }
    
    /**
     * 📁 File Upload Failed
     */
    public static function fileUploadFailed($error = '') {
        return self::error("File upload failed" . ($error ? ": {$error}" : ""), 'Upload Failed');
    }
    
    /**
     * 💾 Data Saved
     */
    public static function dataSaved() {
        return self::success("Data saved successfully", 'Saved');
    }
    
    /**
     * 💾 Data Updated
     */
    public static function dataUpdated() {
        return self::success("Data updated successfully", 'Updated');
    }
    
    /**
     * 🗑️ Data Deleted
     */
    public static function dataDeleted() {
        return self::warning("Data deleted successfully", 'Deleted');
    }
    
    /**
     * 🛒 Added to Cart
     */
    public static function addedToCart($item = '') {
        return self::success("Item added to cart" . ($item ? ": {$item}" : ""), 'Added to Cart');
    }
    
    /**
     * 📦 Order Placed
     */
    public static function orderPlaced($orderNumber = '') {
        return self::success("Order placed successfully" . ($orderNumber ? ". Order #: {$orderNumber}" : ""), 'Order Confirmed');
    }
    
    /**
     * 📚 Quiz Completed
     */
    public static function quizCompleted($score = '') {
        return self::achievement("Quiz completed! Your score: {$score}", 'Quiz Complete');
    }
    
    /**
     * ✅ Correct Answer
     */
    public static function correctAnswer() {
        return self::success("Correct answer!", 'Well Done!');
    }
    
    /**
     * ❌ Wrong Answer
     */
    public static function wrongAnswer() {
        return self::error("Incorrect answer. Try again!", 'Wrong Answer');
    }
    
    /**
     * 🎮 Level Up
     */
    public static function levelUp($level = '') {
        return self::achievement("Congratulations! You've reached level {$level}!", 'Level Up!');
    }
    
    /**
     * 🏆 Points Earned
     */
    public static function pointsEarned($points = '') {
        return self::success("You earned {$points} points!", 'Points Earned');
    }

    // ==========================================
    // 🎯 SPECIALIZED NOTIFICATION FUNCTIONS
    // ==========================================

    /**
     * 🔄 Update Progress
     */
    public static function updateProgress($id, $percentage, $message = '') {
        return self::updateMessage($id, [
            'percentage' => $percentage,
            'message' => $message ?: "Progress: {$percentage}%"
        ]);
    }

    /**
     * ✅ Complete Progress
     */
    public static function completeProgress($id, $message = 'Completed!') {
        return self::updateMessage($id, [
            'percentage' => 100,
            'message' => $message,
            'type' => 'success'
        ]);
    }

    /**
     * ❌ Fail Progress
     */
    public static function failProgress($id, $message = 'Failed!') {
        return self::updateMessage($id, [
            'message' => $message,
            'type' => 'error'
        ]);
    }

    /**
     * 🎯 Input Prompt
     */
    public static function prompt($message, $title = 'Input Required', $defaultValue = '', $onSubmit = '', $options = []) {
        $options['type'] = 'prompt';
        $options['backdrop'] = true;
        $options['dismissible'] = false;
        $options['defaultValue'] = $defaultValue;
        $options['onSubmit'] = $onSubmit;
        $options['inputType'] = $options['inputType'] ?? 'text'; // text, password, email, number
        $options['placeholder'] = $options['placeholder'] ?? '';
        $options['required'] = $options['required'] ?? true;
        return self::add('prompt', $message, $title, $options);
    }

    /**
     * 📅 Date Picker
     */
    public static function datePicker($message, $title = 'Select Date', $onSelect = '', $options = []) {
        $options['type'] = 'datepicker';
        $options['backdrop'] = true;
        $options['onSelect'] = $onSelect;
        $options['format'] = $options['format'] ?? 'YYYY-MM-DD';
        $options['minDate'] = $options['minDate'] ?? '';
        $options['maxDate'] = $options['maxDate'] ?? '';
        return self::add('datepicker', $message, $title, $options);
    }

    /**
     * 📋 List Selector
     */
    public static function listSelector($message, $title = 'Select Option', $items = [], $onSelect = '', $options = []) {
        $options['type'] = 'list';
        $options['backdrop'] = true;
        $options['items'] = $items;
        $options['onSelect'] = $onSelect;
        $options['multiple'] = $options['multiple'] ?? false;
        $options['searchable'] = $options['searchable'] ?? false;
        return self::add('list', $message, $title, $options);
    }

    /**
     * 🎨 Color Picker
     */
    public static function colorPicker($message, $title = 'Select Color', $defaultColor = '#000000', $onSelect = '', $options = []) {
        $options['type'] = 'colorpicker';
        $options['backdrop'] = true;
        $options['defaultColor'] = $defaultColor;
        $options['onSelect'] = $onSelect;
        $options['palette'] = $options['palette'] ?? [];
        return self::add('colorpicker', $message, $title, $options);
    }

    /**
     * 📁 File Selector
     */
    public static function fileSelector($message, $title = 'Select File', $onSelect = '', $options = []) {
        $options['type'] = 'fileselect';
        $options['backdrop'] = true;
        $options['onSelect'] = $onSelect;
        $options['accept'] = $options['accept'] ?? '*/*';
        $options['multiple'] = $options['multiple'] ?? false;
        $options['maxSize'] = $options['maxSize'] ?? '10MB';
        return self::add('fileselect', $message, $title, $options);
    }

    /**
     * 🖼️ Image Preview
     */
    public static function imagePreview($imageUrl, $title = 'Image Preview', $options = []) {
        $options['type'] = 'imagepreview';
        $options['backdrop'] = true;
        $options['imageUrl'] = $imageUrl;
        $options['downloadable'] = $options['downloadable'] ?? true;
        $options['zoomable'] = $options['zoomable'] ?? true;
        return self::add('imagepreview', $title, '', $options);
    }

    /**
     * 📹 Video Player
     */
    public static function videoPlayer($videoUrl, $title = 'Video Player', $options = []) {
        $options['type'] = 'videoplayer';
        $options['backdrop'] = true;
        $options['videoUrl'] = $videoUrl;
        $options['autoplay'] = $options['autoplay'] ?? false;
        $options['controls'] = $options['controls'] ?? true;
        return self::add('videoplayer', $title, '', $options);
    }

    /**
     * 📊 Chart Display
     */
    public static function chart($data, $title = 'Chart', $chartType = 'bar', $options = []) {
        $options['type'] = 'chart';
        $options['backdrop'] = true;
        $options['chartData'] = $data;
        $options['chartType'] = $chartType; // bar, line, pie, doughnut
        $options['responsive'] = $options['responsive'] ?? true;
        return self::add('chart', $title, '', $options);
    }

    /**
     * 🗓️ Calendar
     */
    public static function calendar($events = [], $title = 'Calendar', $options = []) {
        $options['type'] = 'calendar';
        $options['backdrop'] = true;
        $options['events'] = $events;
        $options['view'] = $options['view'] ?? 'month'; // month, week, day
        $options['editable'] = $options['editable'] ?? false;
        return self::add('calendar', $title, '', $options);
    }
    
    /**
     * 🎯 Add Message to Session
     */
    private static function add($type, $message, $title, $options) {
        $id = 'flash_' . uniqid() . '_' . time();
        
        $flashMessage = [
            'id' => $id,
            'type' => $type,
            'message' => htmlspecialchars($message, ENT_QUOTES, 'UTF-8'),
            'title' => htmlspecialchars($title, ENT_QUOTES, 'UTF-8'),
            'options' => $options,
            'timestamp' => time()
        ];
        
        $_SESSION['flash_messages'][] = $flashMessage;
        
        // Limit number of messages
        if (count($_SESSION['flash_messages']) > self::$maxMessages) {
            array_shift($_SESSION['flash_messages']);
        }
        
        return $id;
    }
    
    /**
     * 📋 Get All Messages
     */
    public static function getMessages() {
        return $_SESSION['flash_messages'] ?? [];
    }
    
    /**
     * 🗑️ Clear All Messages
     */
    public static function clear() {
        $_SESSION['flash_messages'] = [];
    }
    
    /**
     * 📊 Count Messages
     */
    public static function count() {
        return count($_SESSION['flash_messages'] ?? []);
    }
    
    /**
     * 🔍 Has Messages
     */
    public static function hasMessages() {
        return self::count() > 0;
    }

    /**
     * 🔄 Update Message
     */
    public static function updateMessage($id, $updates) {
        if (isset($_SESSION['flash_messages'])) {
            foreach ($_SESSION['flash_messages'] as &$message) {
                if ($message['id'] === $id) {
                    foreach ($updates as $key => $value) {
                        if ($key === 'message') {
                            $message['message'] = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                        } elseif ($key === 'title') {
                            $message['title'] = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                        } else {
                            $message['options'][$key] = $value;
                        }
                    }
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 🗑️ Remove Message by ID
     */
    public static function removeMessage($id) {
        if (isset($_SESSION['flash_messages'])) {
            $_SESSION['flash_messages'] = array_filter($_SESSION['flash_messages'], function($msg) use ($id) {
                return $msg['id'] !== $id;
            });
            $_SESSION['flash_messages'] = array_values($_SESSION['flash_messages']);
            return true;
        }
        return false;
    }

    /**
     * 🔍 Get Message by ID
     */
    public static function getMessage($id) {
        if (isset($_SESSION['flash_messages'])) {
            foreach ($_SESSION['flash_messages'] as $message) {
                if ($message['id'] === $id) {
                    return $message;
                }
            }
        }
        return null;
    }

    /**
     * 📋 Get Messages by Type
     */
    public static function getMessagesByType($type) {
        $messages = self::getMessages();
        return array_filter($messages, function($msg) use ($type) {
            return $msg['type'] === $type;
        });
    }

    /**
     * 🎯 Close Loading
     */
    public static function closeLoading($id) {
        return self::removeMessage($id);
    }

    /**
     * 🎯 Close Progress
     */
    public static function closeProgress($id) {
        return self::removeMessage($id);
    }

    /**
     * 🎯 Close Modal
     */
    public static function closeModal($id) {
        return self::removeMessage($id);
    }

    /**
     * 🎯 Close All Modals
     */
    public static function closeAllModals() {
        if (isset($_SESSION['flash_messages'])) {
            $_SESSION['flash_messages'] = array_filter($_SESSION['flash_messages'], function($msg) {
                return !in_array($msg['type'], ['modal', 'alert', 'confirm', 'progress', 'loading']);
            });
            $_SESSION['flash_messages'] = array_values($_SESSION['flash_messages']);
        }
    }

    /**
     * 🎨 Set Theme
     */
    public static function setTheme($theme) {
        self::$theme = $theme;
    }

    /**
     * 📍 Set Position
     */
    public static function setPosition($position) {
        self::$position = $position;
    }

    /**
     * ⚙️ Configure Settings
     */
    public static function configure($settings) {
        foreach ($settings as $key => $value) {
            if (property_exists(__CLASS__, $key)) {
                self::$$key = $value;
            }
        }
    }

    /**
     * 📊 Get Statistics
     */
    public static function getStats() {
        $messages = self::getMessages();
        $stats = [
            'total' => count($messages),
            'by_type' => []
        ];

        foreach ($messages as $message) {
            $type = $message['type'];
            $stats['by_type'][$type] = ($stats['by_type'][$type] ?? 0) + 1;
        }

        return $stats;
    }
    
    /**
     * 🎨 Render Messages
     */
    public static function render($clearAfterRender = true) {
        $messages = self::getMessages();
        
        if (empty($messages)) {
            return '';
        }
        
        $html = self::generateCSS();
        $html .= self::generateHTML($messages);
        $html .= self::generateJS();
        
        if ($clearAfterRender) {
            self::clear();
        }
        
        return $html;
    }
    
    /**
     * 🎨 Generate CSS
     */
    private static function generateCSS() {
        $theme = self::$theme;
        $position = self::$position;
        
        // Position styles
        $positionCSS = self::getPositionCSS($position);
        
        return "
        <style id='ultimate-flash-css'>
        .ultimate-flash-container {
            position: fixed;
            z-index: 999999;
            pointer-events: none;
            font-family: system-ui, -apple-system, sans-serif;
            {$positionCSS}
        }
        
        .ultimate-flash-message {
            pointer-events: auto;
            position: relative;
            margin-bottom: 10px;
            max-width: 400px;
            min-width: 300px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1), 0 2px 4px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
            overflow: hidden;
            cursor: " . (self::$clickToDismiss ? 'pointer' : 'default') . ";
            opacity: 0;
            transform: translateX(100%);
        }
        
        .ultimate-flash-message.show {
            opacity: 1;
            transform: translateX(0);
        }
        
        .ultimate-flash-content {
            padding: 16px 20px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }
        
        .ultimate-flash-icon {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
            margin-top: 2px;
        }
        
        .ultimate-flash-body {
            flex: 1;
            min-width: 0;
        }
        
        .ultimate-flash-title {
            font-weight: 600;
            margin-bottom: 4px;
            font-size: 1.1em;
        }
        
        .ultimate-flash-text {
            line-height: 1.5;
            opacity: 0.9;
        }
        
        .ultimate-flash-close {
            position: absolute;
            top: 8px;
            right: 8px;
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            opacity: 0.7;
            transition: opacity 0.2s;
            font-size: 18px;
            line-height: 1;
        }
        
        .ultimate-flash-close:hover {
            opacity: 1;
        }
        
        .ultimate-flash-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: rgba(255, 255, 255, 0.3);
            transition: width linear;
            width: 100%;
        }
        
        /* Theme Styles */
        " . self::getThemeCSS($theme) . "
        
        /* Responsive */
        @media (max-width: 768px) {
            .ultimate-flash-container {
                left: 10px !important;
                right: 10px !important;
                width: auto !important;
            }
            
            .ultimate-flash-message {
                max-width: none;
                min-width: none;
                width: 100%;
            }
        }
        
        /* Animations */
        @keyframes flash-shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .ultimate-flash-message.shake {
            animation: flash-shake 0.5s ease-in-out;
        }
        
        @keyframes flash-bounce {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .ultimate-flash-message.bounce {
            animation: flash-bounce 0.6s ease-out;
        }

        /* Modal & Alert Styles */
        .ultimate-flash-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: " . self::$backdropColor . ";
            z-index: 999998;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ultimate-flash-modal {
            background: white;
            border-radius: 12px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            max-width: 90vw;
            max-height: 90vh;
            overflow: auto;
            position: relative;
        }

        .ultimate-flash-modal.small { max-width: 400px; }
        .ultimate-flash-modal.medium { max-width: 600px; }
        .ultimate-flash-modal.large { max-width: 800px; }
        .ultimate-flash-modal.fullscreen { max-width: 95vw; max-height: 95vh; }

        .ultimate-flash-modal-header {
            padding: 20px 24px 0;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 20px;
        }

        .ultimate-flash-modal-body {
            padding: 0 24px 20px;
        }

        .ultimate-flash-modal-footer {
            padding: 20px 24px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .ultimate-flash-btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .ultimate-flash-btn-primary {
            background: #3b82f6;
            color: white;
        }

        .ultimate-flash-btn-secondary {
            background: #6b7280;
            color: white;
        }

        .ultimate-flash-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        /* Progress Styles */
        .ultimate-flash-progress-container {
            background: #f3f4f6;
            border-radius: 8px;
            height: 20px;
            overflow: hidden;
            margin: 10px 0;
        }

        .ultimate-flash-progress-bar {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 8px;
        }

        .ultimate-flash-progress-text {
            text-align: center;
            margin-top: 10px;
            font-size: 14px;
            color: #6b7280;
        }

        /* Loading Spinner */
        .ultimate-flash-spinner {
            width: 40px;
            height: 40px;
            margin: 20px auto;
        }

        .ultimate-flash-spinner.default {
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .ultimate-flash-spinner.dots::after {
            content: '⠋';
            font-size: 40px;
            animation: dots 1s linear infinite;
        }

        .ultimate-flash-spinner.pulse {
            background: #3b82f6;
            border-radius: 50%;
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes dots {
            0% { content: '⠋'; }
            12.5% { content: '⠙'; }
            25% { content: '⠹'; }
            37.5% { content: '⠸'; }
            50% { content: '⠼'; }
            62.5% { content: '⠴'; }
            75% { content: '⠦'; }
            87.5% { content: '⠧'; }
            100% { content: '⠇'; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.7; }
        }

        /* Input Styles */
        .ultimate-flash-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            margin: 10px 0;
        }

        .ultimate-flash-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Tooltip Styles */
        .ultimate-flash-tooltip {
            position: absolute;
            background: #1f2937;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 999999;
            pointer-events: none;
        }

        .ultimate-flash-tooltip::after {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            border: 5px solid transparent;
        }

        .ultimate-flash-tooltip.top::after {
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-top-color: #1f2937;
        }

        .ultimate-flash-tooltip.bottom::after {
            bottom: 100%;
            left: 50%;
            margin-left: -5px;
            border-bottom-color: #1f2937;
        }

        /* Animation Styles */
        " . self::getAnimationCSS() . "

        </style>
        ";
    }

    /**
     * 📍 Get Position CSS
     */
    private static function getPositionCSS($position) {
        switch ($position) {
            case 'top-right':
                return 'top: 20px; right: 20px;';
            case 'top-left':
                return 'top: 20px; left: 20px;';
            case 'top-center':
                return 'top: 20px; left: 50%; transform: translateX(-50%);';
            case 'bottom-right':
                return 'bottom: 20px; right: 20px;';
            case 'bottom-left':
                return 'bottom: 20px; left: 20px;';
            case 'bottom-center':
                return 'bottom: 20px; left: 50%; transform: translateX(-50%);';
            default:
                return 'top: 20px; right: 20px;';
        }
    }

    /**
     * 🎭 Get Animation CSS
     */
    private static function getAnimationCSS() {
        if (!self::$enableAnimations) {
            return '';
        }

        $style = self::$animationStyle;

        switch ($style) {
            case 'slide':
                return "
                .ultimate-flash-enter { transform: translateX(100%); opacity: 0; }
                .ultimate-flash-enter-active { transform: translateX(0); opacity: 1; }
                .ultimate-flash-exit { transform: translateX(0); opacity: 1; }
                .ultimate-flash-exit-active { transform: translateX(100%); opacity: 0; }
                ";

            case 'fade':
                return "
                .ultimate-flash-enter { opacity: 0; }
                .ultimate-flash-enter-active { opacity: 1; }
                .ultimate-flash-exit { opacity: 1; }
                .ultimate-flash-exit-active { opacity: 0; }
                ";

            case 'bounce':
                return "
                .ultimate-flash-enter { transform: scale(0.3); opacity: 0; }
                .ultimate-flash-enter-active { transform: scale(1); opacity: 1; }
                .ultimate-flash-exit { transform: scale(1); opacity: 1; }
                .ultimate-flash-exit-active { transform: scale(0.3); opacity: 0; }
                ";

            case 'zoom':
                return "
                .ultimate-flash-enter { transform: scale(0); opacity: 0; }
                .ultimate-flash-enter-active { transform: scale(1); opacity: 1; }
                .ultimate-flash-exit { transform: scale(1); opacity: 1; }
                .ultimate-flash-exit-active { transform: scale(0); opacity: 0; }
                ";

            case 'flip':
                return "
                .ultimate-flash-enter { transform: rotateY(90deg); opacity: 0; }
                .ultimate-flash-enter-active { transform: rotateY(0); opacity: 1; }
                .ultimate-flash-exit { transform: rotateY(0); opacity: 1; }
                .ultimate-flash-exit-active { transform: rotateY(90deg); opacity: 0; }
                ";

            default:
                return "
                .ultimate-flash-enter { transform: translateX(100%); opacity: 0; }
                .ultimate-flash-enter-active { transform: translateX(0); opacity: 1; }
                .ultimate-flash-exit { transform: translateX(0); opacity: 1; }
                .ultimate-flash-exit-active { transform: translateX(100%); opacity: 0; }
                ";
        }
    }

    /**
     * 🎨 Get Theme CSS
     */
    private static function getThemeCSS($theme) {
        switch ($theme) {
            case 'modern':
                return "
                .ultimate-flash-message.success { background: linear-gradient(135deg, #10b981, #059669); color: white; }
                .ultimate-flash-message.error { background: linear-gradient(135deg, #ef4444, #dc2626); color: white; }
                .ultimate-flash-message.warning { background: linear-gradient(135deg, #f59e0b, #d97706); color: white; }
                .ultimate-flash-message.info { background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; }
                .ultimate-flash-message.achievement { background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white; }
                ";

            case 'glass':
                return "
                .ultimate-flash-message {
                    background: rgba(255, 255, 255, 0.1);
                    backdrop-filter: blur(20px);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    color: #333;
                }
                .ultimate-flash-message.success { border-left: 4px solid #10b981; }
                .ultimate-flash-message.error { border-left: 4px solid #ef4444; }
                .ultimate-flash-message.warning { border-left: 4px solid #f59e0b; }
                .ultimate-flash-message.info { border-left: 4px solid #3b82f6; }
                .ultimate-flash-message.achievement { border-left: 4px solid #8b5cf6; }
                ";

            case 'neon':
                return "
                .ultimate-flash-message {
                    background: #000;
                    border: 2px solid;
                    box-shadow: 0 0 20px currentColor;
                }
                .ultimate-flash-message.success { border-color: #06ffa5; color: #06ffa5; }
                .ultimate-flash-message.error { border-color: #ff073a; color: #ff073a; }
                .ultimate-flash-message.warning { border-color: #ffbe0b; color: #ffbe0b; }
                .ultimate-flash-message.info { border-color: #3a86ff; color: #3a86ff; }
                .ultimate-flash-message.achievement { border-color: #ff006e; color: #ff006e; }
                ";

            case 'minimal':
                return "
                .ultimate-flash-message { background: white; color: #333; border: 1px solid #e5e7eb; }
                .ultimate-flash-message.success { border-left: 4px solid #10b981; }
                .ultimate-flash-message.error { border-left: 4px solid #ef4444; }
                .ultimate-flash-message.warning { border-left: 4px solid #f59e0b; }
                .ultimate-flash-message.info { border-left: 4px solid #3b82f6; }
                .ultimate-flash-message.achievement { border-left: 4px solid #8b5cf6; }
                .ultimate-flash-modal { background: white; color: #333; }
                ";

            case 'dark':
                return "
                .ultimate-flash-message { background: #1f2937; color: white; border: 1px solid #374151; }
                .ultimate-flash-message.success { border-left: 4px solid #10b981; }
                .ultimate-flash-message.error { border-left: 4px solid #ef4444; }
                .ultimate-flash-message.warning { border-left: 4px solid #f59e0b; }
                .ultimate-flash-message.info { border-left: 4px solid #3b82f6; }
                .ultimate-flash-message.achievement { border-left: 4px solid #8b5cf6; }
                .ultimate-flash-modal { background: #1f2937; color: white; }
                .ultimate-flash-input { background: #374151; color: white; border-color: #4b5563; }
                ";

            case 'light':
                return "
                .ultimate-flash-message { background: #f9fafb; color: #111827; border: 1px solid #e5e7eb; }
                .ultimate-flash-message.success { background: #ecfdf5; border-color: #10b981; color: #065f46; }
                .ultimate-flash-message.error { background: #fef2f2; border-color: #ef4444; color: #991b1b; }
                .ultimate-flash-message.warning { background: #fffbeb; border-color: #f59e0b; color: #92400e; }
                .ultimate-flash-message.info { background: #eff6ff; border-color: #3b82f6; color: #1e40af; }
                .ultimate-flash-message.achievement { background: #f5f3ff; border-color: #8b5cf6; color: #5b21b6; }
                .ultimate-flash-modal { background: #ffffff; color: #111827; }
                ";

            default:
                return "
                .ultimate-flash-message.success { background: #10b981; color: white; }
                .ultimate-flash-message.error { background: #ef4444; color: white; }
                .ultimate-flash-message.warning { background: #f59e0b; color: white; }
                .ultimate-flash-message.info { background: #3b82f6; color: white; }
                .ultimate-flash-message.achievement { background: #8b5cf6; color: white; }
                .ultimate-flash-message.alert { background: #dc2626; color: white; }
                .ultimate-flash-message.modal { background: white; color: #333; }
                .ultimate-flash-message.confirm { background: #f59e0b; color: white; }
                .ultimate-flash-message.progress { background: white; color: #333; }
                .ultimate-flash-message.loading { background: white; color: #333; }
                .ultimate-flash-message.custom { background: #6b7280; color: white; }
                ";
        }
    }

    /**
     * 🏗️ Generate HTML
     */
    private static function generateHTML($messages) {
        $html = '<div id="ultimate-flash-container" class="ultimate-flash-container">';

        foreach ($messages as $message) {
            $type = $message['type'];
            $options = $message['options'];

            // Check if it's a modal-type message
            if (in_array($type, ['modal', 'alert', 'confirm', 'progress', 'loading', 'prompt', 'datepicker', 'list', 'colorpicker', 'fileselect', 'imagepreview', 'videoplayer', 'chart', 'calendar'])) {
                $html .= self::generateModalHTML($message);
            } elseif ($type === 'tooltip') {
                $html .= self::generateTooltipHTML($message);
            } else {
                $html .= self::generateToastHTML($message);
            }
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * 🍞 Generate Toast HTML
     */
    private static function generateToastHTML($message) {
        $icon = self::getIcon($message['type']);
        $effect = $message['options']['effect'] ?? '';
        $extraClass = '';

        if ($message['type'] === 'error') {
            $extraClass = 'shake';
        } elseif ($effect === 'confetti') {
            $extraClass = 'bounce';
        }

        $html = "
        <div class='ultimate-flash-message {$message['type']} {$extraClass}' data-id='{$message['id']}'>
            <div class='ultimate-flash-content'>
                <div class='ultimate-flash-icon'>{$icon}</div>
                <div class='ultimate-flash-body'>
                    <div class='ultimate-flash-title'>{$message['title']}</div>
                    <div class='ultimate-flash-text'>{$message['message']}</div>
                </div>
                <button class='ultimate-flash-close' onclick='UltimateFlashJS.dismiss(\"{$message['id']}\")'>&times;</button>
            </div>";

        if (self::$showProgress && ($message['options']['persistent'] ?? false) === false) {
            $html .= "<div class='ultimate-flash-progress'></div>";
        }

        $html .= "</div>";

        return $html;
    }

    /**
     * 🪟 Generate Modal HTML
     */
    private static function generateModalHTML($message) {
        $type = $message['type'];
        $options = $message['options'];
        $size = $options['size'] ?? 'medium';
        $backdrop = ($options['backdrop'] ?? true) ? 'ultimate-flash-backdrop' : '';

        $html = "<div class='{$backdrop}' data-id='{$message['id']}'>";
        $html .= "<div class='ultimate-flash-modal {$size}'>";

        // Header
        if ($message['title']) {
            $html .= "<div class='ultimate-flash-modal-header'>";
            $html .= "<h3>{$message['title']}</h3>";
            if ($options['dismissible'] ?? true) {
                $html .= "<button class='ultimate-flash-close' onclick='UltimateFlashJS.dismiss(\"{$message['id']}\")'>&times;</button>";
            }
            $html .= "</div>";
        }

        // Body
        $html .= "<div class='ultimate-flash-modal-body'>";
        $html .= self::generateModalBody($message);
        $html .= "</div>";

        // Footer
        if (in_array($type, ['confirm', 'prompt', 'alert'])) {
            $html .= "<div class='ultimate-flash-modal-footer'>";
            $html .= self::generateModalFooter($message);
            $html .= "</div>";
        }

        $html .= "</div></div>";

        return $html;
    }

    /**
     * 📝 Generate Modal Body
     */
    private static function generateModalBody($message) {
        $type = $message['type'];
        $options = $message['options'];
        $content = $message['message'];

        switch ($type) {
            case 'progress':
                $percentage = $options['percentage'] ?? 0;
                $showPercentage = $options['showPercentage'] ?? true;
                return "
                    <p>{$content}</p>
                    <div class='ultimate-flash-progress-container'>
                        <div class='ultimate-flash-progress-bar' style='width: {$percentage}%'></div>
                    </div>
                    " . ($showPercentage ? "<div class='ultimate-flash-progress-text'>{$percentage}%</div>" : "");

            case 'loading':
                $spinner = $options['spinner'] ?? 'default';
                return "
                    <div class='ultimate-flash-spinner {$spinner}'></div>
                    <p style='text-align: center;'>{$content}</p>";

            case 'prompt':
                $inputType = $options['inputType'] ?? 'text';
                $placeholder = $options['placeholder'] ?? '';
                $defaultValue = $options['defaultValue'] ?? '';
                $required = $options['required'] ?? true;
                return "
                    <p>{$content}</p>
                    <input type='{$inputType}' class='ultimate-flash-input'
                           placeholder='{$placeholder}' value='{$defaultValue}'
                           " . ($required ? 'required' : '') . "
                           data-id='{$message['id']}'>";

            case 'imagepreview':
                $imageUrl = $options['imageUrl'] ?? '';
                $downloadable = $options['downloadable'] ?? true;
                $zoomable = $options['zoomable'] ?? true;
                return "
                    <img src='{$imageUrl}' style='max-width: 100%; height: auto;' alt='Preview'>
                    " . ($downloadable ? "<p><a href='{$imageUrl}' download>Download Image</a></p>" : "");

            case 'videoplayer':
                $videoUrl = $options['videoUrl'] ?? '';
                $autoplay = $options['autoplay'] ?? false;
                $controls = $options['controls'] ?? true;
                return "
                    <video style='width: 100%; height: auto;'
                           " . ($controls ? 'controls' : '') . "
                           " . ($autoplay ? 'autoplay' : '') . ">
                        <source src='{$videoUrl}' type='video/mp4'>
                        Your browser does not support the video tag.
                    </video>";

            default:
                return "<p>{$content}</p>";
        }
    }

    /**
     * 🦶 Generate Modal Footer
     */
    private static function generateModalFooter($message) {
        $type = $message['type'];
        $options = $message['options'];

        switch ($type) {
            case 'confirm':
                $confirmText = $options['confirmText'] ?? 'Confirm';
                $cancelText = $options['cancelText'] ?? 'Cancel';
                return "
                    <button class='ultimate-flash-btn ultimate-flash-btn-secondary'
                            onclick='UltimateFlashJS.handleCancel(\"{$message['id']}\")'>
                        {$cancelText}
                    </button>
                    <button class='ultimate-flash-btn ultimate-flash-btn-primary'
                            onclick='UltimateFlashJS.handleConfirm(\"{$message['id']}\")'>
                        {$confirmText}
                    </button>";

            case 'prompt':
                return "
                    <button class='ultimate-flash-btn ultimate-flash-btn-secondary'
                            onclick='UltimateFlashJS.dismiss(\"{$message['id']}\")'>
                        Cancel
                    </button>
                    <button class='ultimate-flash-btn ultimate-flash-btn-primary'
                            onclick='UltimateFlashJS.handlePromptSubmit(\"{$message['id']}\")'>
                        Submit
                    </button>";

            case 'alert':
                return "
                    <button class='ultimate-flash-btn ultimate-flash-btn-primary'
                            onclick='UltimateFlashJS.dismiss(\"{$message['id']}\")'>
                        OK
                    </button>";

            default:
                return "";
        }
    }

    /**
     * 💬 Generate Tooltip HTML
     */
    private static function generateTooltipHTML($message) {
        $position = $message['options']['position'] ?? 'top';
        return "
        <div class='ultimate-flash-tooltip {$position}' data-id='{$message['id']}'>
            {$message['message']}
        </div>";
    }

    /**
     * 🎯 Get Icon
     */
    private static function getIcon($type) {
        $icons = [
            'success' => '✓',
            'error' => '✕',
            'warning' => '⚠',
            'info' => 'ℹ',
            'achievement' => '🏆',
            'alert' => '🚨',
            'modal' => '📋',
            'popup' => '💬',
            'confirm' => '❓',
            'progress' => '📊',
            'loading' => '⏳',
            'notification' => '🔔',
            'tooltip' => '💡',
            'prompt' => '✏️',
            'datepicker' => '📅',
            'list' => '📋',
            'colorpicker' => '🎨',
            'fileselect' => '📁',
            'imagepreview' => '🖼️',
            'videoplayer' => '📹',
            'chart' => '📊',
            'calendar' => '🗓️',
            'custom' => '⭐'
        ];

        return $icons[$type] ?? $icons['info'];
    }

    /**
     * 🎮 Generate JavaScript
     */
    private static function generateJS() {
        $autoHide = self::$autoHide ? 'true' : 'false';
        $duration = self::$duration;
        $clickToDismiss = self::$clickToDismiss ? 'true' : 'false';
        $showProgress = self::$showProgress ? 'true' : 'false';

        return "
        <script id='ultimate-flash-js'>
        window.UltimateFlashJS = {
            config: {
                autoHide: {$autoHide},
                duration: {$duration},
                clickToDismiss: {$clickToDismiss},
                showProgress: {$showProgress}
            },

            init: function() {
                const messages = document.querySelectorAll('.ultimate-flash-message');
                messages.forEach((message, index) => {
                    setTimeout(() => {
                        message.classList.add('show');
                        this.setupMessage(message);
                    }, index * 150);
                });
            },

            setupMessage: function(message) {
                const messageId = message.dataset.id;

                // Click to dismiss
                if (this.config.clickToDismiss) {
                    message.addEventListener('click', (e) => {
                        if (!e.target.closest('.ultimate-flash-close')) {
                            this.dismiss(messageId);
                        }
                    });
                }

                // Auto hide
                if (this.config.autoHide) {
                    this.scheduleAutoHide(message, messageId);
                }

                // Pause on hover
                message.addEventListener('mouseenter', () => {
                    if (message.timer) {
                        clearTimeout(message.timer);
                    }
                });

                message.addEventListener('mouseleave', () => {
                    if (this.config.autoHide) {
                        this.scheduleAutoHide(message, messageId);
                    }
                });
            },

            scheduleAutoHide: function(message, messageId) {
                const progressBar = message.querySelector('.ultimate-flash-progress');

                if (progressBar && this.config.showProgress) {
                    progressBar.style.transitionDuration = this.config.duration + 'ms';
                    setTimeout(() => {
                        progressBar.style.width = '0%';
                    }, 10);
                }

                message.timer = setTimeout(() => {
                    this.dismiss(messageId);
                }, this.config.duration);
            },

            dismiss: function(messageId) {
                const message = document.querySelector('[data-id=\"' + messageId + '\"]');
                if (message) {
                    message.style.opacity = '0';
                    message.style.transform = 'translateX(100%)';

                    setTimeout(() => {
                        if (message.parentNode) {
                            message.parentNode.removeChild(message);
                        }
                    }, 300);
                }
            },

            dismissAll: function() {
                const messages = document.querySelectorAll('.ultimate-flash-message, .ultimate-flash-backdrop');
                messages.forEach(message => {
                    const messageId = message.dataset.id;
                    this.dismiss(messageId);
                });
            },

            handleConfirm: function(messageId) {
                const message = document.querySelector('[data-id=\"' + messageId + '\"]');
                if (message) {
                    // Execute confirm callback if exists
                    const onConfirm = message.dataset.onConfirm;
                    if (onConfirm && typeof window[onConfirm] === 'function') {
                        window[onConfirm]();
                    }
                    this.dismiss(messageId);
                }
            },

            handleCancel: function(messageId) {
                const message = document.querySelector('[data-id=\"' + messageId + '\"]');
                if (message) {
                    // Execute cancel callback if exists
                    const onCancel = message.dataset.onCancel;
                    if (onCancel && typeof window[onCancel] === 'function') {
                        window[onCancel]();
                    }
                    this.dismiss(messageId);
                }
            },

            handlePromptSubmit: function(messageId) {
                const input = document.querySelector('[data-id=\"' + messageId + '\"]');
                if (input) {
                    const value = input.value;
                    if (input.required && !value.trim()) {
                        alert('This field is required');
                        return;
                    }

                    // Execute submit callback if exists
                    const onSubmit = input.dataset.onSubmit;
                    if (onSubmit && typeof window[onSubmit] === 'function') {
                        window[onSubmit](value);
                    }
                    this.dismiss(messageId);
                }
            },

            updateProgress: function(messageId, percentage, message) {
                const progressBar = document.querySelector('[data-id=\"' + messageId + '\"] .ultimate-flash-progress-bar');
                const progressText = document.querySelector('[data-id=\"' + messageId + '\"] .ultimate-flash-progress-text');
                const messageText = document.querySelector('[data-id=\"' + messageId + '\"] .ultimate-flash-modal-body p');

                if (progressBar) {
                    progressBar.style.width = percentage + '%';
                }
                if (progressText) {
                    progressText.textContent = percentage + '%';
                }
                if (messageText && message) {
                    messageText.textContent = message;
                }
            },

            showTooltip: function(element, message, position) {
                const tooltip = document.createElement('div');
                tooltip.className = 'ultimate-flash-tooltip ' + (position || 'top');
                tooltip.textContent = message;
                document.body.appendChild(tooltip);

                const rect = element.getBoundingClientRect();
                const tooltipRect = tooltip.getBoundingClientRect();

                let left, top;

                switch (position) {
                    case 'bottom':
                        left = rect.left + (rect.width - tooltipRect.width) / 2;
                        top = rect.bottom + 5;
                        break;
                    case 'left':
                        left = rect.left - tooltipRect.width - 5;
                        top = rect.top + (rect.height - tooltipRect.height) / 2;
                        break;
                    case 'right':
                        left = rect.right + 5;
                        top = rect.top + (rect.height - tooltipRect.height) / 2;
                        break;
                    default: // top
                        left = rect.left + (rect.width - tooltipRect.width) / 2;
                        top = rect.top - tooltipRect.height - 5;
                }

                tooltip.style.left = left + 'px';
                tooltip.style.top = top + 'px';

                setTimeout(() => {
                    if (tooltip.parentNode) {
                        tooltip.parentNode.removeChild(tooltip);
                    }
                }, 3000);
            },

            playSound: function(type) {
                if (!this.config.enableSounds) return;

                try {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    const frequencies = {
                        success: 800,
                        error: 300,
                        warning: 600,
                        info: 500,
                        achievement: 1000,
                        alert: 400,
                        confirm: 700
                    };

                    oscillator.frequency.setValueAtTime(frequencies[type] || 500, audioContext.currentTime);
                    oscillator.type = 'sine';

                    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.2);
                } catch (e) {
                    // Sound failed, continue silently
                }
            }
        };

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => UltimateFlashJS.init());
        } else {
            UltimateFlashJS.init();
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                UltimateFlashJS.dismissAll();
            }
        });
        </script>
        ";
    }
}

// ==========================================
// 🎯 GLOBAL CONVENIENCE FUNCTIONS
// ==========================================

if (!function_exists('flash_success')) {
    function flash_success($message, $title = 'Success', $options = []) {
        return UltimateFlash::success($message, $title, $options);
    }
}

if (!function_exists('flash_error')) {
    function flash_error($message, $title = 'Error', $options = []) {
        return UltimateFlash::error($message, $title, $options);
    }
}

if (!function_exists('flash_warning')) {
    function flash_warning($message, $title = 'Warning', $options = []) {
        return UltimateFlash::warning($message, $title, $options);
    }
}

if (!function_exists('flash_info')) {
    function flash_info($message, $title = 'Info', $options = []) {
        return UltimateFlash::info($message, $title, $options);
    }
}

if (!function_exists('flash_achievement')) {
    function flash_achievement($message, $title = 'Achievement!', $options = []) {
        return UltimateFlash::achievement($message, $title, $options);
    }
}

if (!function_exists('flash_render')) {
    function flash_render($clearAfterRender = true) {
        return UltimateFlash::render($clearAfterRender);
    }
}

if (!function_exists('flash_clear')) {
    function flash_clear() {
        return UltimateFlash::clear();
    }
}

if (!function_exists('flash_count')) {
    function flash_count() {
        return UltimateFlash::count();
    }
}

// Modal & Dialog Functions
if (!function_exists('flash_alert')) {
    function flash_alert($message, $title = 'Alert', $options = []) {
        return UltimateFlash::alert($message, $title, $options);
    }
}

if (!function_exists('flash_modal')) {
    function flash_modal($message, $title = 'Modal', $options = []) {
        return UltimateFlash::modal($message, $title, $options);
    }
}

if (!function_exists('flash_popup')) {
    function flash_popup($message, $title = 'Popup', $options = []) {
        return UltimateFlash::popup($message, $title, $options);
    }
}

if (!function_exists('flash_confirm')) {
    function flash_confirm($message, $title = 'Confirm', $onConfirm = '', $onCancel = '', $options = []) {
        return UltimateFlash::confirm($message, $title, $onConfirm, $onCancel, $options);
    }
}

if (!function_exists('flash_progress')) {
    function flash_progress($message, $title = 'Loading...', $percentage = 0, $options = []) {
        return UltimateFlash::progress($message, $title, $percentage, $options);
    }
}

if (!function_exists('flash_loading')) {
    function flash_loading($message = 'Loading...', $title = '', $options = []) {
        return UltimateFlash::loading($message, $title, $options);
    }
}

if (!function_exists('flash_prompt')) {
    function flash_prompt($message, $title = 'Input Required', $defaultValue = '', $onSubmit = '', $options = []) {
        return UltimateFlash::prompt($message, $title, $defaultValue, $onSubmit, $options);
    }
}

if (!function_exists('flash_notification')) {
    function flash_notification($message, $title = 'Notification', $options = []) {
        return UltimateFlash::notification($message, $title, $options);
    }
}

if (!function_exists('flash_tooltip')) {
    function flash_tooltip($message, $element = '', $options = []) {
        return UltimateFlash::tooltip($message, $element, $options);
    }
}

if (!function_exists('flash_custom')) {
    function flash_custom($type, $message, $title = '', $options = []) {
        return UltimateFlash::custom($type, $message, $title, $options);
    }
}

// Utility Functions
if (!function_exists('flash_update_progress')) {
    function flash_update_progress($id, $percentage, $message = '') {
        return UltimateFlash::updateProgress($id, $percentage, $message);
    }
}

if (!function_exists('flash_complete_progress')) {
    function flash_complete_progress($id, $message = 'Completed!') {
        return UltimateFlash::completeProgress($id, $message);
    }
}

if (!function_exists('flash_close_loading')) {
    function flash_close_loading($id) {
        return UltimateFlash::closeLoading($id);
    }
}

if (!function_exists('flash_close_modal')) {
    function flash_close_modal($id) {
        return UltimateFlash::closeModal($id);
    }
}

if (!function_exists('flash_close_all_modals')) {
    function flash_close_all_modals() {
        return UltimateFlash::closeAllModals();
    }
}

if (!function_exists('flash_set_theme')) {
    function flash_set_theme($theme) {
        return UltimateFlash::setTheme($theme);
    }
}

if (!function_exists('flash_set_position')) {
    function flash_set_position($position) {
        return UltimateFlash::setPosition($position);
    }
}

if (!function_exists('flash_configure')) {
    function flash_configure($settings) {
        return UltimateFlash::configure($settings);
    }
}

if (!function_exists('flash_stats')) {
    function flash_stats() {
        return UltimateFlash::getStats();
    }
}

// Quick aliases
if (!function_exists('flash_ok')) {
    function flash_ok($message = 'Success!') {
        return UltimateFlash::success($message);
    }
}

if (!function_exists('flash_fail')) {
    function flash_fail($message = 'Error!') {
        return UltimateFlash::error($message);
    }
}

// ==========================================
// 🚀 AUTO-RENDER (Optional)
// ==========================================

// Uncomment the line below to auto-render messages
// echo UltimateFlash::render();

?>
    }
