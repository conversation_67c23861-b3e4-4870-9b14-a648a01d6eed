<?php
/**
 * 🧪 VALIDATION TEST - Check if Ultimate Flash Messages works correctly
 */

// Include the ultimate flash system
require_once __DIR__ . '/ultimate-flash.php';

// Test basic functionality
$errors = [];
$success = [];

// Test 1: Basic message creation
try {
    $id1 = flash_success('Test success message');
    if ($id1) {
        $success[] = "✅ Basic success message creation works";
    } else {
        $errors[] = "❌ Basic success message creation failed";
    }
} catch (Exception $e) {
    $errors[] = "❌ Exception in basic message creation: " . $e->getMessage();
}

// Test 2: Modal creation
try {
    $id2 = UltimateFlash::modal('Test modal content', 'Test Modal');
    if ($id2) {
        $success[] = "✅ Modal creation works";
    } else {
        $errors[] = "❌ Modal creation failed";
    }
} catch (Exception $e) {
    $errors[] = "❌ Exception in modal creation: " . $e->getMessage();
}

// Test 3: Progress bar creation
try {
    $id3 = UltimateFlash::progress('Test progress', 'Loading', 50);
    if ($id3) {
        $success[] = "✅ Progress bar creation works";
    } else {
        $errors[] = "❌ Progress bar creation failed";
    }
} catch (Exception $e) {
    $errors[] = "❌ Exception in progress creation: " . $e->getMessage();
}

// Test 4: Configuration
try {
    UltimateFlash::setTheme('dark');
    UltimateFlash::setPosition('top-center');
    $success[] = "✅ Configuration functions work";
} catch (Exception $e) {
    $errors[] = "❌ Exception in configuration: " . $e->getMessage();
}

// Test 5: Statistics
try {
    $stats = UltimateFlash::getStats();
    if (is_array($stats) && isset($stats['total'])) {
        $success[] = "✅ Statistics function works (Total: {$stats['total']})";
    } else {
        $errors[] = "❌ Statistics function failed";
    }
} catch (Exception $e) {
    $errors[] = "❌ Exception in statistics: " . $e->getMessage();
}

// Test 6: Rendering
try {
    $html = UltimateFlash::render(false); // Don't clear messages
    if (is_string($html) && !empty($html)) {
        $success[] = "✅ Rendering function works";
    } else {
        $errors[] = "❌ Rendering function failed";
    }
} catch (Exception $e) {
    $errors[] = "❌ Exception in rendering: " . $e->getMessage();
}

// Test 7: Global functions
try {
    flash_error('Test error');
    flash_warning('Test warning');
    flash_info('Test info');
    $success[] = "✅ Global functions work";
} catch (Exception $e) {
    $errors[] = "❌ Exception in global functions: " . $e->getMessage();
}

// Clear messages for clean test
UltimateFlash::clear();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultimate Flash Messages - Validation Test</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            color: white;
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .test-results {
            margin: 20px 0;
        }
        
        .success-item {
            background: rgba(16, 185, 129, 0.2);
            border-left: 4px solid #10b981;
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .error-item {
            background: rgba(239, 68, 68, 0.2);
            border-left: 4px solid #ef4444;
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .summary {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Validation Test Results</h1>
        
        <div class="summary">
            <h2>📊 Test Summary</h2>
            <p><strong>Total Tests:</strong> <?= count($success) + count($errors) ?></p>
            <p><strong>✅ Passed:</strong> <?= count($success) ?></p>
            <p><strong>❌ Failed:</strong> <?= count($errors) ?></p>
            <p><strong>Success Rate:</strong> <?= round((count($success) / (count($success) + count($errors))) * 100, 1) ?>%</p>
        </div>
        
        <?php if (!empty($success)): ?>
        <div class="test-results">
            <h3>✅ Successful Tests</h3>
            <?php foreach ($success as $item): ?>
                <div class="success-item"><?= $item ?></div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
        <div class="test-results">
            <h3>❌ Failed Tests</h3>
            <?php foreach ($errors as $item): ?>
                <div class="error-item"><?= $item ?></div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <div class="summary">
            <h3>🎯 System Status</h3>
            <?php if (empty($errors)): ?>
                <p style="color: #10b981; font-size: 1.2em;">
                    🎉 <strong>ALL TESTS PASSED!</strong><br>
                    Ultimate Flash Messages is working perfectly!
                </p>
            <?php else: ?>
                <p style="color: #ef4444; font-size: 1.2em;">
                    ⚠️ <strong>SOME TESTS FAILED</strong><br>
                    Please check the errors above.
                </p>
            <?php endif; ?>
            
            <div style="margin-top: 20px;">
                <a href="simple-test.php" class="btn">🧪 Interactive Test</a>
                <a href="validation-test.php" class="btn">🔄 Run Again</a>
            </div>
        </div>
        
        <div class="summary">
            <h3>📋 System Information</h3>
            <p><strong>PHP Version:</strong> <?= PHP_VERSION ?></p>
            <p><strong>Session Status:</strong> <?= session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive' ?></p>
            <p><strong>Memory Usage:</strong> <?= round(memory_get_usage() / 1024 / 1024, 2) ?> MB</p>
            <p><strong>File Size:</strong> <?= round(filesize(__DIR__ . '/ultimate-flash.php') / 1024, 2) ?> KB</p>
        </div>
    </div>
    
    <script>
        console.log('🧪 Validation Test Completed');
        console.log('✅ Passed: <?= count($success) ?>');
        console.log('❌ Failed: <?= count($errors) ?>');
        console.log('📊 Success Rate: <?= round((count($success) / (count($success) + count($errors))) * 100, 1) ?>%');
    </script>
</body>
</html>
